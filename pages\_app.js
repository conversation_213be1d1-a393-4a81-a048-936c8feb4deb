import { useEffect } from "react";
import { useRouter } from "next/router";
import "@/styles/utility.css";
import "@/styles/globals.css";
import "@/styles/arabic.css";
import Layout from "@/components/layout";
import DasboardLayout from "@/components/DashboardLayout"
import Lenis from "@studio-freight/lenis";


const menuItems = [
	"/dashboard",
	"/requested-services",
	"/invoice",
	"/account-information",
];
export default function App({ Component, pageProps }) {
    const router = useRouter();
	const isSelectedRoute = menuItems.includes(router.pathname);
    
    useEffect(() => {
        // Initialize Lenis
        const lenis = new Lenis({
          duration: 1.5, // Adjusting the duration to be slightly longer for smoother scrolling
          easing: (t) => 1 - Math.pow(1 - t, 4), // Smoother easing curve for a more natural feel
          smooth: true, // Enable smooth scroll
          smoothTouch: true, // Enable smooth scrolling on touch devices
        });
        // Animation frame loop
        const raf = (time) => {
          lenis.raf(time);
          requestAnimationFrame(raf);
        };
        requestAnimationFrame(raf);
        return () => {
          lenis.destroy();
        };
      }, []);

    useEffect(() => {
        const path = router.pathname.replace("/", "") || "home";
        const body = document.body; 

        // Remove existing page-related classes
        body.className = body.className
            .split(" ")
            .filter(cls => !cls.startsWith("body-"))
            .join(" ");

        // Add the class for the current page
        body.classList.add(`body-${path}`);

        // Cleanup function to remove the class on route change or component unmount
        return () => {
            body.classList.remove(`body-${path}`);
        };
    }, [router.pathname]);
    return (
        <>
        {isSelectedRoute ? (
            <DasboardLayout>
                <Component {...pageProps} />
            </DasboardLayout>
        ) : (
            <Layout {...pageProps}>
                <Component {...pageProps} />
            </Layout>
        )}
        </>
        
        
    );
}
