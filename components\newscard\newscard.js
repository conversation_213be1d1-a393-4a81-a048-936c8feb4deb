import React from "react";
import Image from "next/image";
import Link from "next/link";
import styles from "./newscard.module.css";
import { useRouter } from "next/router";

const Newscard = ({ item }) => {
  const formatDate = (dateString, locale = "en") => {
    const date = new Date(dateString);
    const day = date.getDate();
    const month = date.toLocaleString(locale, { month: "long" });
    const year = date.getFullYear();

    return `${day}, ${month}, ${year}`;
  };
  const { locale } = useRouter();
  const langCode = locale === "ar" ? "ar" : "en";

  return (
    <div className={styles.newsslider_item}>
      <div className={`${styles.newsslider_image} zoom-effect`}>
        <span className={styles.news_tag}>News</span>
        <Link href="/news">
          <Image 
                loading='lazy'
           src={item.acf?.listing} alt="news" width={380} height={420} />
        </Link>
      </div>
      <div className={styles.newsslider_content}>
        <span
          className={
            langCode === "ar"
              ? styles.newsslider_date_ar
              : styles.newsslider_date
          }
        >
          {formatDate(item.date, langCode)}
        </span>
        <p className={styles.newsslider_discription}>
          <Link
            href={`/news/${item.slug}`}
            dangerouslySetInnerHTML={{ __html: item.title.rendered }}
            className="limitNo"
          />
        </p>
      </div>
    </div>
  );
};

export default Newscard;
