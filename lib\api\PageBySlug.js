// API for fetching page data

export const fetchPageBySlug = async (slug, langCode) => {
  let url = `${process.env.NEXT_PUBLIC_API_BASE_URL}/pages?slug=${slug}&acf_format=standard&lang=${langCode}`;
  
  // console.log(`Fetching from URL: ${url}`);
  
  const response = await fetch(url);
  
  if (!response.ok) {
      console.error('Fetch error:', response.status, response.statusText);
      throw new Error('Network response was not ok');
  }

  const data = await response.json();
  // console.log(data); 

  if (data.length === 0) {
      throw new Error(`No page found for slug: ${slug}`);
  }

  return data[0]; 
};

  
  export const fetchServices = async (langCode) => {
    const response2 = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/services?acf_format=standard&lang=${langCode}`);    
    if (!response2.ok) {
      throw new Error('Network response was not ok');
    }
  
    const data1 = await response2.json();
    return data1; 
  };
  
  export const fetchNews = async (langCode, limit) => { 
    const response2 = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/news?acf_format=standard&orderby=date&order=desc&per_page=${limit}&lang=${langCode}`);
    
    if (!response2.ok) {
        throw new Error('Network response was not ok');
    }

    const data1 = await response2.json();
    return data1;
};
// fetch requests
export const fetchRequest = async (langCode, limit, userId) => { 
  const response3 = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/requests?acf_format=standard&orderby=date&order=desc&per_page=${limit}&lang=${langCode}&author=${userId}`);
  
  if (!response3.ok) {
      throw new Error('Network response was not ok');
  }

  const data3 = await response3.json();
  return data3;
};
// fetch ratings
export const fetchRatings = async (langCode, limit) => { 
  const response4 = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/rating-list?acf_format=standard&orderby=date&order=desc&per_page=${limit}&lang=${langCode}`);
  
  if (!response4.ok) {
      throw new Error('Network response was not ok');
  }

  const data4 = await response4.json();
  return data4;
};
// fetch capc lists
export const fetchCapclist = async (langCode, limit) => { 
  const response4 = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/capc-requests?acf_format=standard&orderby=date&order=desc&per_page=${limit}&lang=${langCode}`);
  
  if (!response4.ok) {
      throw new Error('Network response was not ok');
  }

  const data4 = await response4.json();
  return data4;
};
  // Single
export const fetchByPost = async (postype, slug, langCode) => {
  if (!slug) {
      throw new Error('Slug is required');
  }

  const apiUrl = `${process.env.NEXT_PUBLIC_API_BASE_URL}/${postype}?slug=${slug}&acf_format=standard&lang=${langCode}`;
  //console.log('Fetching URL:', apiUrl); 

  const response = await fetch(apiUrl);

  if (!response.ok) {
      throw new Error(`Network response was not ok: ${response.statusText}`);
  }
  
  const data = await response.json();
  return data[0];
};
  

// Related

export const fetchRelatedNews = async (langCode, limit, exclude, catid) => {
	if (!catid) {
		return []; 
	}

	const response2 = await fetch(
		`${process.env.NEXT_PUBLIC_API_BASE_URL}/news?news-type=${catid}&exclude=${exclude}&per_page=${limit}&lang=${langCode}&acf_format=standard`
	);

	if (!response2.ok) {
		throw new Error("Network response was not ok");
	}

	const data1 = await response2.json();
	return data1;
};


// career
export const fetchCareer = async (langCode) => {
  const response2 = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/careers?acf_format=standard&lang=${langCode}`);    
  if (!response2.ok) {
    throw new Error('Network response was not ok');
  }

  const data1 = await response2.json();
  return data1; 
};