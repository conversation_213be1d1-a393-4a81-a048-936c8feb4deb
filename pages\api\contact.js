export default async function handler(req, res) {
    if (req.method !== "POST") {
      return res.status(405).json({ error: "Method not allowed" });
    }
  
    const { name, email, message, recaptchaToken } = req.body;
    
    if (!recaptchaToken) {
      return res.status(400).json({ error: "reCAPTCHA token is missing" });
    }
  
    try {
      const verifyUrl = `https://www.google.com/recaptcha/api/siteverify`;
      const response = await fetch(verifyUrl, {
        method: "POST",
        headers: { "Content-Type": "application/x-www-form-urlencoded" },
        body: new URLSearchParams({
          secret: "6LdsYQArAAAAAAnFclH5kT2bDYVfSHZtDfMuvHFu", // 🔹 Use your reCAPTCHA secret key here
          response: recaptchaToken,
        }),
      });
  
      const data = await response.json();
  
      if (!data.success) {
        return res.status(400).json({ error: "reCAPTCHA validation failed", details: data });
      }
  
      // ✅ reCAPTCHA passed - Process the form submission here
      return res.status(200).json({ success: true, message: "Form submitted successfully!" });
    } catch (error) {
      console.error("Error verifying reCAPTCHA:", error);
      return res.status(500).json({ error: "Internal server error" });
    }
  }
  