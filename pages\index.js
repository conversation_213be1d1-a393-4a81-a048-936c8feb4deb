import Head from "next/head";
import Link from "next/link";
import Image from "next/image";
import styles from "@/styles/Home.module.css";
import HomeBanner from "@/components/banner/homeBanner";
import Membercards from "@/components/membercards/membercards";
import Newsslider from "@/components/newsslider/newsslider";
import Threecolumn from "@/components/threecolumn/threecolumn";
import Sixcolumn from "@/components/sixcolumn/sixcolumn";
import Ratings from "@/components/ratings/ratings";
import Counter from "@/components/counter/counter";
import { fetchPageBySlug, fetchServices } from "@/lib/api/PageBySlug";
import styles2 from "@/pages/services/service.module.css";
import { useRouter } from "next/router";
import { fetchNONews } from "@/lib/api/byfield";

export default function Home({ pageData, serviceList, noText }) {
	const overview = pageData.acf.overview_details || "";
	const rating = pageData.acf.details_rating || "";
	const services = pageData.acf.details_under || "";
	const why = pageData.acf.details_why || "";

	const { locale } = useRouter(); 
	const langCode = locale === 'ar' ? 'ar' : 'en';

	return (
    <>
      <>
        <HomeBanner bannerData={pageData.acf.banner_details} ticker={pageData.acf.tickers} />
        {/* ********************************************************************************************************* */}
        {overview && (
          <section className={`${styles.home_counter} py-120 bg-secondary`}>
            <div className="container">
              <div className={styles.home_counter_content}>
                <div
                  className={styles.home_counter_content_left}
                  data-aos="fade-right"
                >
                  <span
                    dangerouslySetInnerHTML={{ __html: overview.sub_title }}
                  />
                  <h2 dangerouslySetInnerHTML={{ __html: overview.title }} />
                </div>
                <div
                  className={styles.home_counter_content_right}
                  data-aos="fade-left"
                >
                  <p
                    dangerouslySetInnerHTML={{
                      __html: overview.short_description,
                    }}
                  />
                </div>
              </div>

              {overview.counts.length > 0 && (
                <Counter langcode={langCode} count={overview.counts} />
              )}

              <div className="text-center mt-50">
                {overview.button && overview.button.url && (
                  <Link
                    data-aos="fade-up"
                    href={overview.button.url}
                    className={`${styles.btn_style_wrap} btn_style_wrap  m-auto`}
                  >
                    <span className="btn_style_primary">
                      {overview.button.title}
                    </span>
                    <span
                      className={`${
                        langCode === "ar" ? "btn_style_arrow_ar" : ""
                      } ${"btn_style_arrow"}`}
                    >
                      <Image
                        src={
                          langCode === "ar"
                            ? "/images/white_arrow_left.png"
                            : "/images/white_right_arw.svg"
                        }
                        alt="icon"
                        width={22}
                        height={15}
                        priority
                      />
                    </span>
                  </Link>
                )}
              </div>
            </div>
          </section>
        )}

        {/* ********************************************************************** */}
        {rating && (
          <section className={`${styles.home_ratings} py-120 bg-secondary`}>
            <div className="container">
              <div
                className={`${styles.home_ratings_title} text-white text-center mb-50`}
                data-aos="fade-up"
              >
                <h2
                  className="main_title"
                  dangerouslySetInnerHTML={{ __html: rating.title }}
                />
                <p
                  dangerouslySetInnerHTML={{ __html: rating.short_description }}
                />
              </div>
              {rating.sliders.length > 0 && <Ratings slides={rating.sliders} />}

              <div
                className={`${
                  langCode === "ar"
                    ? styles.rating_sevices_ar
                    : styles.rating_sevices
                } rating_sevices`}
              >
                <div className="d-flex-center d-flex-xbetween text-white mb-50">
                  <div data-aos="fade-right">
                    <h2
                      className="main_title mr-t-20"
                      dangerouslySetInnerHTML={{ __html: services.title_2 }}
                    />
                    <p
                      dangerouslySetInnerHTML={{ __html: services.description }}
                    />
                  </div>
                  {services.button && services.button.url && (
                    <Link
                      data-aos="fade-left"
                      href={services.button.url}
                      className={`${styles.btn_style_wrap} btn_style_wrap`}
                    >
                      <span className="btn_style_primary">
                        {services.button.title}
                      </span>
                      <span
                        className={`${
                          langCode === "ar" ? "btn_style_arrow_ar" : ""
                        } ${"btn_style_arrow"}`}
                      >
                        <Image
                          src={
                            langCode === "ar"
                              ? "/images/white_arrow_left.png"
                              : "/images/white_right_arw.svg"
                          }
                          alt="icon"
                          width={22}
                          height={15}
                          priority
                        />
                      </span>
                    </Link>
                  )}
                </div>
                {services && (
                  <Sixcolumn
                    sixColumnItems={serviceList}
                    list={true}
                    className={styles2.sixcolumn}
                    lang={langCode}
                  />
                )}
              </div>
            </div>
          </section>
        )}
        {/* *********************************************************************** */}
        <section
          className={`${styles.section_rating_sevices1} py-120 bg-secondary section_rating_sevices1`}
          style={{ display: "none" }}
        >
          <dvv className="container">
            <div className="credit-rating">
              <div className="mb-50 text-white text-center" data-aos="fade-up">
                <h2
                  className="main_title"
                  dangerouslySetInnerHTML={{ __html: services.main_title }}
                />
                <p
                  dangerouslySetInnerHTML={{
                    __html: services.main_description,
                  }}
                />
              </div>
            </div>
          </dvv>
        </section>
        {/* ******************************************************************************* */}
        <section className="py-120 bg-light">
          <div className="container">
            <div className="title mb-50 text-center" data-aos="fade-up">
              <h2
                className="main_title text-secondary mb-15"
                dangerouslySetInnerHTML={{ __html: why.title }}
              />
              <p
                className={styles.p_font_weight}
                dangerouslySetInnerHTML={{ __html: why.description }}
              />
            </div>
            {why.cards.length > 0 && <Threecolumn columns={why.cards} />}
          </div>
        </section>
        {/* ************************************************************************************ */}
        <section className="py-120 bg-secondary home-news">
          <div className="container">
            <div className="title mb-50" data-aos="fade-up">
              <h2 className="main_title text-white">
                {pageData.acf.titles.title_news}
              </h2>
            </div>
            <div data-aos="fade-up">
              <Newsslider text={noText} />
            </div>
          </div>
        </section>
        <section className="py-120">
          <div className="container">
            <Membercards />
          </div>
        </section>

        <div
          className={
            langCode === "en"
              ? styles.how_to_help_box
              : styles.how_to_help_box_ar
          }
        >
          <div className="container">
            <Link
              className={styles.phone_box_container}
              href={`tel:${pageData.acf.number}`}
            >
              <span className={styles.phone_txt_box} >
                {pageData.acf.text}
              </span>
              <span className={` ${styles.phone_box} ${styles.pulse}`}>
                <Image
                  src="/images/help_phone.svg"
                  alt="icon"
                  width={24}
                  height={24} loading='lazy'
                />
              </span>
            </Link>
          </div>
        </div>
      </>
    </>
  );
}

export async function getServerSideProps({ locale }) {
	const langCode = locale === "ar" ? "ar" : "en";
	try {
		const pageData = await fetchPageBySlug("home", langCode);
		const serviceList = await fetchServices(langCode);
    const noText = await fetchNONews(langCode );

		return {
			props: {
				pageData,
				serviceList,
        noText
			},
		};
	} catch (error) {
		console.error("Failed to fetch data:", error);
		return {
			props: {
				pageData: null,
			},
		};
	}
}
