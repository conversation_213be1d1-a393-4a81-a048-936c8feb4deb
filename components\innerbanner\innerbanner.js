import React, { useEffect, useRef } from "react";
import { gsap } from "gsap";
import styles from "./innerbanner.module.css";

const SubBanner = ({bannerDetails,alignEnd}) => {
	const bannerRef = useRef(null);

	useEffect(() => {
		if (bannerRef.current) {
			gsap.fromTo(
				bannerRef.current,
				{ backgroundSize: "150% 150%" },
				{
					backgroundSize: "cover",
					duration: 1.5,
					ease: "power2.out",
				}
			);
		}
	}, []);

	const alignmentClass = alignEnd
		? styles["align-end"]
		: styles["align-center"];

	return (
		<>
			<section
				ref={bannerRef}
				className={styles.subbanner}
				style={{
					backgroundImage: `url(${bannerDetails.image})`,
					backgroundSize: "cover",
					backgroundPosition: "center",
					backgroundRepeat: "no-repeat",
				}}
			>
				<div className={`${styles.container} container ${alignmentClass}`}>
					<div data-aos="fade-up">
						{bannerDetails.sub_title && <p dangerouslySetInnerHTML={{ __html: bannerDetails.sub_title }} />}
						<h1 className="banner_text text-white"
							dangerouslySetInnerHTML={{ __html: bannerDetails.title }}
						/>
					</div>
				</div>
			</section>
		</>
	);
};

export default SubBanner;
