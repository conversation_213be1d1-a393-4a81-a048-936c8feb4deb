@font-face {
  font-family: "GE SS Two Bold - [UrduFonts.com]";
  src: url("../public/fonts/arabic/GE\ SS\ Two\ Bold\ -\ [UrduFonts.com].woff2") format("woff2"),
    url("../public/fonts/arabic/GE\ SS\ Two\ Bold\ -\ [UrduFonts.com].woff") format("woff");
  font-weight: normal;
  font-style: normal;
  font-display: swap;

}

@font-face {
  font-family: "GE SS Two Light - [UrduFonts.com]";
  src: url("../public/fonts/arabic/GE\ SS\ Two\ Light\ -\ [UrduFonts.com].woff2") format("woff2"),
    url("../public/fonts/arabic/GE\ SS\ Two\ Light\ -\ [UrduFonts.com].woff") format("woff");
  font-weight: normal;
  font-style: normal;
  font-display: swap;

}

@font-face {
  font-family: "GE SS Two Medium - [UrduFonts.com]";
  src: url("../public/fonts/arabic/GE\ SS\ Two\ Medium\ -\ [UrduFonts.com].woff2") format("woff2"),
    url("../public/fonts/arabic/GE\ SS\ Two\ Medium\ -\ [UrduFonts.com].woff") format("woff");
  font-weight: normal;
  font-style: normal;
  font-display: swap;

}

:root {

  --font-arabic: "GE SS Two Medium - [UrduFonts.com]";
}

.rtl html,
.rtl body {
  max-width: 100vw;
  color: #000;
  font-size: 16px;
  font-weight: 300;
  background: #ffffff;
  font-family: var(--font-arabic);
  overflow-x: hidden;
}

.rtl p,
.rtl h1,
.rtl h2,
.rtl h3,
.rtl h4,
.rtl h5,
.rtl h6 {
  font-family: var(--font-arabic);
}

.rtl p {
  font-weight: 100;
}

.rtl .main_title,
.rtl .main_title_center {
  font-weight: 400;
  font-size: 2.8rem;
}

/* ============= Responsive ============ */

@media (max-width: 1200px) {

  .main_title,
  .main_title_center {
    font-size: 2rem;
    line-height: 38px;
  }
}

@media (max-width: 767px) {

  .main_title,
  .main_title_center {
    font-size: 1.75rem;
    line-height: 35px;
  }
}