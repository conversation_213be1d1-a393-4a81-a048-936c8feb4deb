import { useState, useEffect } from "react";
import Image from "next/image";
import styles from "./careerdetail.module.css";
import SubBanner from "@/components/innerbanner/innerbanner";
import { fetchByPost } from "@/lib/api/PageBySlug";

function CareerDetails({pageData, langCode}) {
	const banner = pageData.acf.banner_career_detail || '';

	const getRelativeTime = (date) => {
		const now = new Date();
		const secondsDiff = Math.floor((now - date) / 1000);
		const minutesDiff = Math.floor(secondsDiff / 60);
		const hoursDiff = Math.floor(minutesDiff / 60);
		const daysDiff = Math.floor(hoursDiff / 24);
		const monthsDiff = Math.floor(daysDiff / 30);
		const yearsDiff = Math.floor(daysDiff / 365);
	
		// Translations for both English and Arabic
		const translations = {
			en: {
				second: "second",
				seconds: "seconds",
				minute: "minute",
				minutes: "minutes",
				hour: "hour",
				hours: "hours",
				day: "day",
				days: "days",
				month: "month",
				months: "months",
				year: "year",
				years: "years",
				ago: "ago",
			},
			ar: {
				second: "ثانية",
				seconds: "ثواني",
				minute: "دقيقة",
				minutes: "دقائق",
				hour: "ساعة",
				hours: "ساعات",
				day: "يوم",
				days: "أيام",
				month: "شهر",
				months: "أشهر",
				year: "سنة",
				years: "سنوات",
				ago: "منذ",
			}
		};
	
		const t = translations[langCode]; // Choose the right translation set
	
		if (secondsDiff < 60) {
			return secondsDiff === 1 
				? `1 ${t.second} ${t.ago}` 
				: `${secondsDiff} ${t.seconds} ${t.ago}`;
		} else if (minutesDiff < 60) {
			return minutesDiff === 1 
				? `1 ${t.minute} ${t.ago}` 
				: `${minutesDiff} ${t.minutes} ${t.ago}`;
		} else if (hoursDiff < 24) {
			return hoursDiff === 1 
				? `1 ${t.hour} ${t.ago}` 
				: `${hoursDiff} ${t.hours} ${t.ago}`;
		} else if (daysDiff < 30) {
			return daysDiff === 1 
				? `1 ${t.day} ${t.ago}` 
				: `${daysDiff} ${t.days} ${t.ago}`;
		} else if (monthsDiff < 12) {
			return monthsDiff === 1 
				? `1 ${t.month} ${t.ago}` 
				: `${monthsDiff} ${t.months} ${t.ago}`;
		} else {
			return yearsDiff === 1 
				? `1 ${t.year} ${t.ago}` 
				: `${yearsDiff} ${t.years} ${t.ago}`;
		}
	};
	
	
		const dateObj = new Date(pageData.date);
		const relativeTime = getRelativeTime(dateObj);
	return (
		<div>
			<SubBanner
				bannerDetails={banner}
				alignEnd={false}
			/>
			<div class="bg_main center_bg_2">
				<section
					className={`${styles.careers_top_section} pt-100 bg-secondary`}
				>
					<div className="container relative">
						<div
							class="overview_title text-center margin-center"
							data-aos="fade-up"
						>
							<h2 className="main_title text-white" 
								dangerouslySetInnerHTML={{ __html: pageData.acf.basic_details.title }} 		
							/>
							<p dangerouslySetInnerHTML={{ __html: pageData.acf.basic_details.short_description }}  />
						</div>
					</div>
				</section>
				<section
					className={`${styles.careers_detail_section} pt-50 bg-secondary`}
				>
					<div className="container  relative">
						<div className={`${styles.careers_detail_title} text-white`}>
							<p>Posted {relativeTime}</p>
							<h2 className="main_title" dangerouslySetInnerHTML={{ __html: pageData.title.rendered }}   />
							<ul>
								<li>{pageData.acf.basic_details.department}</li> <li>{pageData.acf.basic_details.location}</li>
							</ul>
						</div>

						<div className={`${styles.career_detail_content_wrap} pt-50`}>
							<div
								className={styles.career_detail_content_left}
								data-aos="fade-right"
							>
								<div className={styles.career_detail_box}>
									<h3 dangerouslySetInnerHTML={{ __html: pageData.acf.responsibilities.title }} />
									<ul>
										{pageData.acf.responsibilities.list.map((list,index) => {

											return (
												<li key={index}
												dangerouslySetInnerHTML={{ __html:list.detail_list }}

												/> )
										})}
										
									</ul>
								</div>

								<div className={styles.career_detail_box}>
									<h3 dangerouslySetInnerHTML={{ __html: pageData.acf.qualifications.title }} />
									<ul>
										{pageData.acf.qualifications.list.map((list,index) => {
										return (
											<li key={index}
											dangerouslySetInnerHTML={{ __html:list.item }}

											/> )
										})}
									</ul>
								</div>
							</div>

							<div
								className={styles.career_detail_content_right}
								data-aos="fade-left"
							>
								<div className={styles.career_detail_box}>
									<h3 dangerouslySetInnerHTML={{ __html: pageData.acf.skills.title}} />
									<ul>
										{pageData.acf.skills.list.map((list,index) => {
											return (
												<li key={index}
												dangerouslySetInnerHTML={{ __html:list.item }}

												/> 
											)
										})}
									</ul>
								</div>
							</div>
						</div>
					</div>
				</section>
			</div>
		</div>
	);
}

export default CareerDetails;

export async function getServerSideProps(context) {
	const { params } = context;
	const slug = params.slug;
	const { locale } = context;    
	const langCode = locale === 'ar' ? 'ar' : 'en'; 
	try {
		const pageData = await fetchByPost("careers", slug, langCode);

		return {
			props: {
				pageData,
				langCode
			},
		};
	} catch (error) {
		console.error("Failed to fetch data:", error);
		return {
			notFound: true,
		};
	}
}