import { useRef, useState } from "react";
import Image from "next/image";
import styles from "./accordion.module.css";

const Accordion = ({ list }) => {
  const [activeIndex, setActiveIndex] = useState(null);
  const contentRefs = useRef([]);

  const toggleAccordion = (index) => {
    const currentContent = contentRefs.current[index];

    if (activeIndex === index) {
      if (currentContent) {
        currentContent.style.height = `${currentContent.scrollHeight}px`; 
        requestAnimationFrame(() => {
          currentContent.style.height = "0px"; 
        });
      }
      setTimeout(() => setActiveIndex(null), 600);
    } else {
      if (currentContent) {
        currentContent.style.height = "0px"; 
        requestAnimationFrame(() => {
          currentContent.style.height = `${currentContent.scrollHeight}px`; 
        });
      }

      if (activeIndex !== null) {
        const previousContent = contentRefs.current[activeIndex];
        if (previousContent) {
          previousContent.style.height = `${previousContent.scrollHeight}px`; 
          requestAnimationFrame(() => {
            previousContent.style.height = "0px"; 
          });
        }
      }

      setActiveIndex(index);
    }
  };

  return (
    <div className={`${styles.accordion}`}>
      {list.map((item, index) => (
        <div key={index} className={styles.accordionItem}>
          <div
            className={`${styles.accordionHeader} ${
              activeIndex === index ? styles.active : ""
            }`}
            onClick={() => toggleAccordion(index)}
          >
            <span className={styles.accordionTitle}>{item.question}</span>
            <span className={styles.icon}>
              <Image
                src="/images/down_wt_arrow.svg"
                alt="arrow"
                width={11}
                height={11}
                loading='lazy'
              />
            </span>
          </div>
          <div
            ref={(el) => (contentRefs.current[index] = el)}
            className={`${styles.accordionContent}`}
            style={{
              height: "0px",
              overflow: "hidden",
              transition: "all 0.6s ease-in-out",
            }}
          >
            {activeIndex === index && <p>{item.answer}</p>}
          </div>
        </div>
      ))}
    </div>
  );
};

export default Accordion;
