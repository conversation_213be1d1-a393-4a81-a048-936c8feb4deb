import React, { useEffect, useState } from "react";
import Head from "next/head";
import Header from "@/components/header/Header";
import Footer from "@/components/footer/Footer";
import { useRouter } from "next/router";
import parse from "html-react-parser";


export default function RootLayout(props) {
	const children = props?.children;
	const yoast_head = props?.pageData?.yoast_head;
	const { locale } = useRouter();
	useEffect(() => {
		if (locale === 'ar') {
			// document.body.classList.add('rtl');
			document.body.classList.remove("ltr", "english-cls");
			document.body.classList.add("rtl", "arabic-cls");
			document.body.setAttribute("dir", "rtl");
		} else {
			// document.body.classList.remove('rtl'); 
			document.body.classList.remove("rtl", "arabic-cls");
			document.body.classList.add("ltr", "english-cls");
			document.body.setAttribute("dir", "");
		}
	}, [locale]);
	return (
		<>
			<Head>
				{yoast_head && parse(yoast_head)}
				<meta name="viewport" content="width=device-width, initial-scale=1" />
				<link rel="icon" href="/favicon.png" />
			</Head>
			<Header>

			</Header>
			{/* <Header /> */}
			<main> {children}</main>

			<Footer login={true}></Footer>
		</>
	);
}
