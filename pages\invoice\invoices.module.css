.invoice_title {
    display: flex;
    width: 100%;
    align-items: center;
    row-gap: 15px;
    justify-content: space-between;
}

.invoice_title h4 {
    color: #ffffff;
    font-size: 1.5625rem;
    line-height: 1;
    font-weight: 400;
    margin: 0;
}

.invoice_title form {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    column-gap: 15px;
    width: 430px;
}

.service_sort {
    width: 50%;
    text-align: center;
    background: transparent;
}

.date_sort {
    border: none;
    width: 45%;
    text-align: center;
    background: var(--color-primary);
}

.invoice_title select {
    height: 50px;
    padding: 0 20px;
    color: #ffffff;
    border-radius: 9px;
}

/* ------------------------------------------------------------------------ */

.table_style_second {
    width: 100%;
}

.table_style_second table {
    border-collapse: collapse;
    background: linear-gradient(180deg,
            rgba(255, 255, 255, 0.12) 0%,
            rgba(0, 84, 135, 0.12) 100%);
    backdrop-filter: blur(12px);
    border-radius: 19px;
}

.table_style_second table th {
    color: #ffffff;
    font-size: 1.125rem;
    padding: 23px 0;
    background: #005487;
    font-weight: 400;
    text-align: left;
    width: 15%;
    min-width: 100px;
}

.table_style_second table th:first-child {
    width: 45%;
}

.table_style_second table th:nth-child(2) {
    width: 20%;
}

.table_style_second table th:nth-child(3) {
    width: 20%;
}

.table_style_second table th:nth-child(4) {
    width: 15%;
}

.table_style_second td:first-child,
.table_style_second th:first-child {
    padding-left: 30px;
}

.table_style_second thead th:first-child {
    border-radius: 20px 0 0 0;
}

.table_style_second thead th:last-child {
    border-radius: 0 20px 0 0;
}

.table_style_second table td {
    color: #ffffff;
    font-size: 1rem;
    padding: 27px 0;
    font-weight: 400;
}

.table_style_second tbody tr {
    padding: 0 20px;
    position: relative;
}

.table_style_second tbody tr::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 20px; 
  right: 20px; 
  height: 1px;
  background: #6d758f;
}

.table_style_second tbody tr:last-child::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 20px; 
  right: 20px; 
  height: 1px;
  background: var(--color-secondary) !important;
}

.pdf_link {
    display: flex;
    align-items: center;
    column-gap: 8px;
    text-transform: uppercase;
}

/* =========== */

.table_style_second_ar {
    width: 100%;
}

.table_style_second_ar table {
    border-collapse: collapse;
    background: linear-gradient(180deg,
            rgba(255, 255, 255, 0.12) 0%,
            rgba(0, 84, 135, 0.12) 100%);
    backdrop-filter: blur(12px);
    border-radius: 19px;
}

.table_style_second_ar table th {
    color: #ffffff;
    font-size: 1.125rem;
    padding: 23px 0;
    background: #005487;
    font-weight: 400;
    text-align: right;
    width: 15%;
}

.table_style_second_ar table th:first-child {
    width: 45%;
}

.table_style_second_ar table th:nth-child(2) {
    width: 20%;
}

.table_style_second_ar table th:nth-child(3) {
    width: 20%;
}

.table_style_second_ar table th:nth-child(4) {
    width: 15%;
}

.table_style_second_ar td:first-child,
.table_style_second_ar th:first-child {
    padding-right: 30px;
}

.table_style_second_ar thead th:first-child {
    border-radius: 0 20px 0 0;
}

.table_style_second_ar thead th:last-child {
    border-radius: 20px 0 0 0;
}

.table_style_second_ar table td {
    color: #ffffff;
    font-size: 1rem;
    padding: 27px 0;
    font-weight: 400;
}

.table_style_second_ar tbody tr {
    padding: 0 20px;
    position: relative;
}

.table_style_second_ar tbody tr:not(:last-child)::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    margin: auto;
    width: calc(100% - 40px);
    height: 1px;
    background: #6D758F;
}

.table_style_second_ar tbody tr:last-child::after {
    content: "";
    background: var(--color-secondary)!important;
}

.pdf_link_ar {
    display: flex;
    align-items: center;
    column-gap: 8px;
    text-transform: uppercase;
    justify-content: flex-start;
}


@media (max-width: 767px) {
    .invoice_title {
        flex-direction: column;
        justify-content: center;
    }

    .table_style_second td:first-child,
    .table_style_second th:first-child {
        padding-left: 15px;
    }
    .table_style_second table th:first-child {
        width: 40%;
    }
    
    .table_style_second table th:nth-child(2) {
        width: 20%;
    }
    
    .table_style_second table th:nth-child(3) {
        width: 20%;
    }
    
    .table_style_second table th:nth-child(4) {
        width: 20%;
    }
    .table_style_second table td {
        padding: 15px 0;
    }
    .table_style_second table th {
        padding: 15px 0;
    }

    /* ========== */

    .table_style_second_ar td:first-child,
    .table_style_second_ar th:first-child {
        padding-left: 15px;
    }
    .table_style_second_ar table th:first-child {
        width: 40%;
    }
    
    .table_style_second_ar table th:nth-child(2) {
        width: 20%;
    }
    
    .table_style_second_ar table th:nth-child(3) {
        width: 20%;
    }
    
    .table_style_second_ar table th:nth-child(4) {
        width: 20%;
    }
    .table_style_second_ar table td {
        padding: 15px 0;
    }
    .table_style_second_ar table th {
        padding: 15px 0;
    }
}