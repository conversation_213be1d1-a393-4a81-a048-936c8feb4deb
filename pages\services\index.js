import { useState, useEffect } from "react";
import Image from "next/image";
import styles from "./service.module.css";
import SubBanner from "@/components/innerbanner/innerbanner";
import Sixcolumn from "@/components/sixcolumn/sixcolumn";
import Pagemenu from "@/components/pagemenu/pagemenu";
import { fetchPageBySlug, fetchServices } from "@/lib/api/PageBySlug";
import Head from "next/head";

function Services({ pageData, serviceList }) {
  //console.log(pageData, "fgdfggf");
  const banner = pageData.acf.banner_details || "";
  return (
    <div>
      <SubBanner bannerDetails={banner} alignEnd={false} />

      {/* <Pagemenu menuItems={pageData.acf.tabs_list.tabs} /> */}

      <section className={`${styles.service_page_wrap} bg-secondary`}>
        <div className={`${styles.relative} container`}>
          <div className=" overview_title text-white pb-50" id="serviceList">
            <h2
              className="main_title mb-20"
              dangerouslySetInnerHTML={{
                __html: pageData.acf.service_details.title,
              }}
            />
            <p
              dangerouslySetInnerHTML={{
                __html: pageData.acf.service_details.description,
              }}
            />
          </div>
          <div id="serviceList">
            <Sixcolumn
              sixColumnItems={serviceList}
              list={true}
              className={styles.sixcolumn}
            />
          </div>
        </div>
      </section>
    </div>
  );
}

export default Services;
export async function getServerSideProps({ locale }) {
  const langCode = locale === "ar" ? "ar" : "en";
  try {
    const pageData = await fetchPageBySlug("services", langCode);
    const serviceList = await fetchServices(langCode);
    return {
      props: {
        pageData,
        serviceList,
      },
    };
  } catch (error) {
    console.error("Failed to fetch data:", error);
    return {
      props: {
        pageData: null,
      },
    };
  }
}
