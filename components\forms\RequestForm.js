import React, { useState, useRef } from "react";
import { countryOptions } from './countryData';
import style from "@/pages/find-rating/findRatings.module.css";
import style2 from "@/pages/contact-us/contact.module.css";
import SelectStyle from "@/components/SelectBox/select";
import Image from 'next/image'; 
import { useRouter } from 'next/router';
import ReCAPTCHA from "react-google-recaptcha";

const RequestForm = ({ services, formLabels }) => {

  const { locale } = useRouter();

  const siteKey = '6LdsYQArAAAAAP2LBpUnQmZOjwzho_nuAqiv6OBM';
  const recaptchaRef = useRef(null);

  const langCode = locale === 'ar' ? 'ar' : 'en';
  const router = useRouter();
  const serviceList = [
    ...services.map(service => ({
      value: service.slug,
      label: service.title.rendered
    })),
    { value: 'others', label: locale === 'ar' ? 'آخرون' : 'Others' }
  ];

  const [formData, setFormData] = useState({
    name: "",
    email: "",
    company: "",
    country: "",
    phonenumber: "",
    servicetype: "",
    message: ""
  });

  const [validationErrors, setValidationErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [responseMessage, setResponseMessage] = useState('');
  const [sresponseMessage, setSresponseMessage] = useState('');
  const [fieldErrors, setFieldErrors] = useState({});
  const membershipType = typeof window !== 'undefined' ? JSON.parse(localStorage.getItem('membershipType')) || '' : '';
  const userId = typeof window !== 'undefined' ? JSON.parse(localStorage.getItem('user')) : '';
  const [isChecked, setIsChecked] = useState(false); 
  const [resetSelect, setResetSelect] = useState(false);
  const [resetSelect1, setResetSelect1] = useState(false);

  const handleCheckboxChange = (event) => {
    setIsChecked(event.target.checked); 
  };

  // const formId = 1863;
  // const unitTag = 'wpcf7-f974-o1';

  const formId = locale === 'ar' ? 1864 : 1863;
	const unitTag = locale === 'ar' ? 'wpcf7-f1864-o1' : 'wpcf7-f1863-o1';

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
    setResponseMessage('');
    setValidationErrors({});
    setSresponseMessage('');
    setFieldErrors({});

    if (name === "phonenumber") {
      const numericValue = value.replace(/\D/g, ""); 
      setFormData({ ...formData, [name]: numericValue });
    } 
    if (name === "FirstName" || name === "LastName") {
      const alphabeticValue = value.replace(/[^a-zA-Z]/g, "");
      setFormData({ ...formData, [name]: alphabeticValue });
    } 
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // if (!localStorage.getItem('token')) {

    //   router.push(`/login?redirect=${router.asPath}`);
    //   return; 
    // }

  
    // if (!membershipType || !userId) {
    //   setResponseMessage('You must be logged in to submit the form.');
    //   return; 
    // }

    setIsSubmitting(true);
    setResponseMessage('');

    const formDataToSend = new FormData();
    formDataToSend.append('namere', formData.name);
    formDataToSend.append('emailre', formData.email);
    formDataToSend.append('company', formData.company);
    formDataToSend.append('country', formData.country);
    formDataToSend.append('phonenumber', formData.phonenumber);
    formDataToSend.append('servicetype', formData.servicetype);
    // formDataToSend.append('membership', membershipType);
    formDataToSend.append('message', formData.message);
    formDataToSend.append("_wpcf7_unit_tag", unitTag);
    // formDataToSend.append('g-recaptcha-response', token); // Add reCAPTCHA token


    if (userId) {
      formDataToSend.append('user_id', userId);
    }

    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_CONTACT_API_URL}/${formId}/feedback`, {
        method: 'POST',
        body: formDataToSend,
        redirect: 'follow',
      });

      const result = await response.json();
      setIsSubmitting(false);

      if (response.ok) {
        if (result.status === "validation_failed") {
          const fieldErrors = result.invalid_fields.reduce((acc, fieldError) => {
            acc[fieldError.field] = fieldError.message;
            return acc;
          }, {});
          setFieldErrors(fieldErrors);
          setTimeout(() => {
            setFieldErrors({});
          }, 3000);
        } else if (result.status === "mail_sent") {
          setSresponseMessage(result.message);
          setTimeout(() => {
            setSresponseMessage('');
          }, 3000);
          setFieldErrors({});
          setFormData({
            name: '', email: '', company: '', country: '', phonenumber: '', servicetype: '', message: ''
          });
          setResetSelect(true);
          setResetSelect1(true);
        } else {
          setResponseMessage('An unexpected error occurred. Please try again.');
          setTimeout(() => {
            setFieldErrors({});
            setResponseMessage('')
          }, 3000);
        }
      } else {
        setResponseMessage(result.message || 'Something went wrong. Please try again.');
        setTimeout(() => {
          setFieldErrors({});
          setResponseMessage('')
        }, 3000);
      }
    } catch (error) {
      console.error('Error:', error);
      setIsSubmitting(false);
      setResponseMessage('An error occurred while submitting the form.');
      setTimeout(() => {
        setResponseMessage('');
      }, 3000);
    }
  };

  return (
    <>
      <form className={style.first_form} onSubmit={handleSubmit}>
        <ul className={style.first_form_list}>
          <li>
            <label>{formLabels.name}</label>
            <input
              type="text"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
            />
            {fieldErrors.namere && (
              <p
                style={{
                  color: "red",
                  marginTop: "5px",
                  fontSize: "12px",
                  padding: "0 8px",
                }}
              >
                {fieldErrors.namere}
              </p>
            )}
          </li>
          <li>
            <label>{formLabels.email}</label>
            <input
              type="email"
              name="email"
              value={formData.email}
              onChange={handleInputChange}
            />
            {fieldErrors.emailre && (
              <p
                style={{
                  color: "red",
                  marginTop: "5px",
                  fontSize: "12px",
                  padding: "0 8px",
                }}
              >
                {fieldErrors.emailre}
              </p>
            )}
          </li>
          <li>
            <label>{formLabels.organization}</label>
            <input
              type="text"
              name="company"
              value={formData.company}
              onChange={handleInputChange}
            />
            {fieldErrors.company && (
              <p
                style={{
                  color: "red",
                  marginTop: "5px",
                  fontSize: "12px",
                  padding: "0 8px",
                }}
              >
                {fieldErrors.company}
              </p>
            )}
          </li>
          <li>
            <label>{formLabels.country}</label>
            <SelectStyle
              options={countryOptions}
              placeholder={locale === "ar" ? "اختر" : "Choose"}
              onChange={(selected) =>
                setFormData((prev) => ({ ...prev, country: selected.label }))
              }
              reset={resetSelect1}
            />
            {fieldErrors.country && (
              <p
                style={{
                  color: "red",
                  marginTop: "5px",
                  fontSize: "12px",
                  padding: "0 8px",
                }}
              >
                {fieldErrors.country}
              </p>
            )}
          </li>
          <li>
            <label>{formLabels.phone_number_}</label>
            <input
              type="text"
              name="phonenumber"
              value={formData.phonenumber}
              onChange={handleInputChange}
            />
            {fieldErrors.phonenumber && (
              <p
                style={{
                  color: "red",
                  marginTop: "5px",
                  fontSize: "12px",
                  padding: "0 8px",
                }}
              >
                {fieldErrors.phonenumber}
              </p>
            )}
          </li>
          <li>
            <label>{formLabels.type_}</label>
            <SelectStyle
              options={serviceList}
              placeholder={locale === "ar" ? "اختر" : "Choose"}
              onChange={(selected) =>
                setFormData((prev) => ({
                  ...prev,
                  servicetype: selected.label,
                }))
              }
              reset={resetSelect}
            />
            {fieldErrors.servicetype && (
              <p
                style={{
                  color: "red",
                  marginTop: "5px",
                  fontSize: "12px",
                  padding: "0 8px",
                }}
              >
                {fieldErrors.servicetype}
              </p>
            )}
          </li>
          <li style={{ width: "100%" }}>
            <label>{formLabels.textarea} </label>
            <textarea
              name="message"
              value={formData.message}
              onChange={handleInputChange}
              style={{
                background: "transparent",
                border: "2px solid #ffffff8f",
                width: "100%",
                padding: "20px",
                borderRadius: "19px",
                color: "white",
              }}
            ></textarea>
            {fieldErrors.message && (
              <p
                style={{
                  color: "red",
                  marginTop: "5px",
                  fontSize: "12px",
                  padding: "0 8px",
                }}
              >
                {fieldErrors.message}
              </p>
            )}
          </li>

          {/* <ReCAPTCHA ref={recaptchaRef} sitekey={siteKey} size="invisible" /> */}

          <li>
            <button
              className={`${style.btn_style_wrap} btn_style_wrap`}
              type="submit"
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <span
                  className={`${
                    langCode === "ar" ? "btn_style_primary_ar" : ""
                  } btn_style_primary`}
                >
                  ...
                </span>
              ) : (
                <span
                  className={`${
                    langCode === "ar" ? "btn_style_primary_ar" : ""
                  } btn_style_primary`}
                >
                  {locale === "ar" ? "إرسال" : "Submit"}
                </span>
              )}
              <span
                className={`${
                  langCode === "ar" ? "btn_style_arrow_ar" : ""
                } ${"btn_style_arrow"}`}
              >
                <Image
                  src={
                    langCode == "ar"
                      ? "/images/white_arrow_left.png"
                      : "/images/white_right_arw.svg"
                  }
                  alt="icon"
                  width={22}
                  height={15}
                  priority
                />
              </span>
            </button>
          </li>
        </ul>
      </form>
      {responseMessage && (
        <p
          style={{
            color: "red",
            marginTop: "5px",
            fontSize: "12px",
            padding: "0 8px",
            textAlign: "center",
          }}
        >
          {responseMessage}
        </p>
      )}
      {sresponseMessage && (
        <p
          style={{
            color: "green",
            marginTop: "5px",
            fontSize: "12px",
            padding: "0 8px",
            textAlign: "center",
          }}
        >
          {sresponseMessage}
        </p>
      )}
    </>
  );
}

export default RequestForm;
