import React, { useEffect, useState } from "react";
import Link from "next/link";
import Image from "next/image";
import styles from "./regulatoryReports.module.css";
import Pagemenu from "@/components/pagemenu/pagemenu";
import SubBanner from "@/components/innerbanner/innerbanner";
import Tablestyle from "@/components/tablestyle/tablestyle";
import { fetchPageBySlug } from "@/lib/api/PageBySlug";
import { fetchaboutTab } from "@/lib/api/byfield";
import SelectStyle from "@/components/SelectBox/select";
import { useRouter } from "next/router";

const sort = [
	{ value: 'date', label: 'Date' },
	{ value: 'description', label: 'Description' },
];

const Index = ({ pageData, Tabs }) => {
	const { locale } = useRouter();
	const banner = pageData.acf.banner_details || '';
	const reports = pageData.acf.reports || '';
	const [selectedOption, setSelectedOption] = useState(null);
	const reprtLIst = reports.report_list || [];
	const [sortOption, setSortOption] = useState();
	const [sortedReports, setSortedReports] = useState(reprtLIst);
	const [options, setOptions] = useState([]);
	const [filtertype, setFiltertype] = useState();

	  const langCode = locale === "ar" ? "ar" : "en";

	const convertDate = (dateString) => {
		const [day, month, year] = dateString.split('-');
		return `${year}-${month}-${day}`; 
	};

	useEffect(() => {
		// Step 1: Sort the list based on sortOption
		const sortedReports = [...reprtLIst].sort((a, b) => {
			if (sortOption === 'date') {
				return new Date(convertDate(b.date_)) - new Date(convertDate(a.date_));
			} else if (sortOption === 'description') {
				return a.type.label.localeCompare(b.type.label);
			}
			return 0;
		});
	
		// Step 2: Filter the sorted list based on filtertype
		let filteredReports;

    if (filtertype && filtertype !== 'all') {
      filteredReports = sortedReports.filter(report => report.type.value === filtertype);
    } else if (filtertype === 'all') {
      filteredReports = sortedReports;
    } else {
      filteredReports = sortedReports;
    }

	
		setSortedReports(filteredReports);

		// console.log(sortOption)
		// console.log(filtertype)
	}, [sortOption, filtertype, reprtLIst, locale]);
	
	useEffect(() => {
			const uniqueOptions = pageData.acf.reports.report_list.reduce((acc, item) => {
				const existingType = acc.find(type => type.value === item.type.value);
				if (!existingType) {
					acc.push({ value: item.type.value, label: item.type.label });
				}
				return acc;
			}, []);
      const allOption = { value: 'all', label: 'All' };
      uniqueOptions.unshift(allOption);

			uniqueOptions.sort((a, b) => {
				if (a.value.includes("Other")) return 1;
				if (b.value.includes("Other")) return -1;
				return 0;
			  });

			setOptions(uniqueOptions);
		
	}, [pageData.acf.report_list]);

	//console.log(options)
	  
	  
	  
	  

	return (
    <>
      <SubBanner bannerDetails={banner} alignEnd={false} />
      <Pagemenu menuItems={Tabs.tabs} />
      <section className="pt-120 bg-secondary">
        <div className="container">
          <div
            className={`${styles.table_sorting_title} table_sorting_title`}
            data-aos="fade-up"
          >
            {/* <Link href={reports.button.url || '#'} className={`${styles.btn_style_wrap} btn_style_wrap`}>
							<span className="btn_style_primary">{reports.button.title}</span>
							<span className="btn_style_arrow">
								<Image src="/images/white_right_arw.svg" alt="icon" width={22} height={15} priority />
							</span>
						</Link> */}

            <form>
              <ul className={styles.table_sorting_ul}>
                <li>
                  {/* <label>{locale === "ar" ? "اكتب :" : "Type :"} </label> */}
                  <div className={`${styles.btn_style_wrap} btn_style_wrap`}>
                    <span className="btn_style_primary">
                      {locale === "ar" ? "اختر خيارًا" : "Select"}
                    </span>
                    <span
                      className={`${'drop_btn_icon'} ${"btn_style_arrow"}`}
                    >
                      <Image
                        src={ "/images/down_wt_arrow.svg"}
                        alt="icon"
                        width={15}
                        height={10}
                        priority
                      />
                    </span>
                    <ul className="inner_drop">
                      {options.map((option) => (
                        <li
                          key={option.value}
                          onClick={() => {
                            setFiltertype(option.value);
                          }}
                        >
                          {option.label}
                        </li>
                      ))}
                    </ul>
                  </div>
                </li>
                <li>
                  <label>
                    {locale === "ar" ? "ترتيب حسب :" : "Sort By :"}{" "}
                  </label>
                  <div className="select_wrap year_select">
                    <SelectStyle
                      options={sort}
                      label="Choose an option"
                      placeholder="Choose"
                      onChange={(option) => setSortOption(option.value)}
                    />
                    {/* {sortOption && (
											<p>Selected: {sortOption}</p> 
										)} */}
                  </div>
                </li>
              </ul>
            </form>
          </div>
          <div data-aos="fade-up">
            <Tablestyle
              documents={sortedReports}
              className={styles.table_two_column}
              regulatory={true}
            />
          </div>
        </div>
      </section>
    </>
  );
};
export async function getServerSideProps({locale}) {
	const langCode = locale === "ar" ? "ar" : "en";
	try {
		const pageData = await fetchPageBySlug("regulatory-reports", langCode);
		const Tabs = await fetchaboutTab(langCode);
		return {
			props: {
				pageData,
				Tabs,
			},
		};
	} catch (error) {
		console.error("Failed to fetch data:", error);
		return {
			props: {
				pageData: null,
			},
		};
	}
}
export default Index;


