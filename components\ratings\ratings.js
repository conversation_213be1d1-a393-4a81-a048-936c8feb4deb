"use client";
import React from "react";
import Link from "next/link";
import Image from "next/image";
import { useState, useEffect } from "react";

import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/dist/ScrollTrigger";

import styles from "./ratings.module.css";
import { useRouter } from "next/router";

const ratings = [
  {
    title: "What is a credit rating?",
    description:
      "Credit rating is a symbolic indicator of a credit rating agency's opinion on the rated entity with reference to its ability and willingness to repay the debt as per terms of the arrangement.",
    link: "#.",
    image: "/images/gsap_img1.jpg",
    buttonText: "Learn More",
    imageSrc: "/images/white_right_arw.svg",
    imageAlt: "icon",
  },
  // You can add more items here
  {
    title: "The rating process",
    description:
      "Learn how ratings and analyses address the relative credit risk of debt instruments and securities across global industries and asset classes.",
    link: "#.",
    image: "/images/gsap_img2.jpg",
    buttonText: "Learn More",
    imageSrc: "/images/white_right_arw.svg",
    imageAlt: "icon",
  },
  {
    title: (
      <>
        The rating <br /> scale
      </>
    ),
    description:
      "Learn how ratings and analyses address the relative credit risk of debt instruments and securities across global industries and asset classes.",
    link: "#.",
    image: "/images/gsap_img3.jpg",
    buttonText: "Learn More",
    imageSrc: "/images/white_right_arw.svg",
    imageAlt: "icon",
  },
];

const Ratings = ({ slides }) => {
  gsap.registerPlugin(ScrollTrigger);

  // State to manage whether the component is mounted
  const [isMounted, setIsMounted] = useState(false);

  const { locale } = useRouter();
  const langCode = locale === "ar" ? "ar" : "en";

  // useEffect(() => {
  // 	// Indicate that the component is mounted on the client
  // 	setIsMounted(true);

  // 	if (typeof window !== "undefined") {
  // 		const cards = gsap.utils.toArray(".card");
  // 		const spacer = 0;
  // 		cards.forEach((card, index) => {
  // 			ScrollTrigger.create({
  // 				trigger: card,
  // 				start: `top-=${index * spacer} top+=200px`,
  // 				endTrigger: ".pin-panel",
  // 				// end: `bottom 0 top+=${cards.length} top-=500px`,
  // 				end: "bottom 50%+=150px",
  // 				pin: true,
  // 				pinSpacing: false,
  // 				invalidateOnRefresh: true,
  // 				animation: gsap.to(card.querySelector(".pin-panel"), {
  // 					opacity: 1,
  // 					duration: 1,
  // 				}),
  // 			});
  // 		});

  // 		return () => {
  // 			ScrollTrigger.getAll().forEach((trigger) => trigger.kill());
  // 			gsap.globalTimeline.clear();
  // 		};
  // 	}
  // }, []);

  useEffect(() => {
    // Indicate that the component is mounted on the client
    setIsMounted(true);

    if (typeof window !== "undefined") {
      const cards = gsap.utils.toArray(".card");
      const mm = gsap.matchMedia(); // Initialize matchMedia

      // Define responsive screen sizes and their respective configurations
      mm.add(
        {
          // Large screens (desktop)
          isDesktop: "(min-width: 1024px)",

          // Tablets
          isTablet: "(min-width: 768px) and (max-width: 1023px)",

          // Mobile screens
          isMobile: "(max-width: 767px)",
        },
        (context) => {
          const { isDesktop, isTablet, isMobile } = context.conditions;

          const spacer = isDesktop ? 0 : isTablet ? 5 : 0; // Adjust spacing based on screen size

          cards.forEach((card, index) => {
            ScrollTrigger.create({
              trigger: card,
              start: `top-=${index * spacer} top+=200px`,
              endTrigger: ".pin-panel",
              end: isMobile ? "bottom 50%+=50px" : "bottom 50%+=150px", // Adjust `end` for mobile
              pin: true,
              pinSpacing: false,
              invalidateOnRefresh: true,
              animation: gsap.to(card.querySelector(".pin-panel"), {
                opacity: 1,
                duration: isMobile ? 100 : 100, // Adjust animation duration for mobile
              }),
            });
          });
        }
      );

      return () => {
        ScrollTrigger.getAll().forEach((trigger) => trigger.kill());
        gsap.globalTimeline.clear();
        mm.revert(); // Revert matchMedia changes
      };
    }
  }, []);

  return (
    <div className="pin-panel">
      <div className={`${styles.rating_cards} cards`}>
        {slides.map((item, index) =>
          item?.slide ? (
            <div
              key={index}
              className={`${langCode === "ar" ? styles.rating_card_item_ar : ""
                } ${styles.rating_card_item_en} card ${index % 2 === 0 ? "bg-white" : "bg-white"
                }`}
              style={{ backgroundImage: `url(${item.slide.image || ""})` }}
            >
              <div className={styles.rating_card_body}>
                <h3 dangerouslySetInnerHTML={{ __html: item.slide.title }} />
                <p
                  dangerouslySetInnerHTML={{ __html: item.slide.description }}
                />
                {item.slide.button && item.slide.button.url && (
                  <Link href={item.slide.button.url} passHref>
                    <span className={`${styles.btn_style_wrap} btn_style_wrap`}>
                      <span className="btn_style_primary">
                        {item.slide.button.title}
                      </span>
                      <span
                        className={`${langCode === "ar" ? "btn_style_arrow_ar" : ""
                          } ${"btn_style_arrow"}`}
                      >
                        <Image
                          src={
                            langCode === "ar"
                              ? "/images/white_arrow_left.png"
                              : "/images/white_right_arw.svg"
                          }
                          alt="arrow icon"
                          width={22}
                          height={15}
                          priority
                        />
                      </span>
                    </span>
                  </Link>
                )}
              </div>
            </div>
          ) : null
        )}
      </div>
    </div>
  );
};

export default Ratings;
