// pages/api/download.js

export default async function handler(req, res) {
    const { url } = req.query; // Get the file URL from query parameters
    
    try {
        const response = await fetch(url);
        if (!response.ok) {
            return res.status(response.status).end(); // Forward the error status
        }
        
        const data = await response.buffer(); // Get the file buffer
        res.setHeader('Content-Disposition', `attachment; filename=${url.split('/').pop()}`); // Set the filename
        res.setHeader('Content-Type', 'application/pdf'); // Set the correct content type
        res.status(200).send(data); // Send the file buffer
    } catch (error) {
        console.error("Error fetching file:", error);
        res.status(500).end();
    }
}
