.contact_banner {
    width: 100%;
    height: 700px;
}

.contact_form_main {
    width: 100%;
    max-width: 820px;
    margin: 0 auto;
}

.contact_form_main h2 {
    margin-bottom: 10px;
}

.form_wrap {
    margin-top: 40px;
}

.form_wrap ul {
    display: flex;
    flex-wrap: wrap;
    width: 100%;
    justify-content: space-between;
    row-gap: 15px;
}

.form_wrap ul li {
    width: 49%;
}

.form_wrap ul li.full_width {
    width: 100%;
    align-items: center;
    display: flex;
    flex-wrap: wrap;
}

.form_main::placeholder {
    color: #111111;
}

.form_main {
    width: 100%;
    height: 50px;
    line-height: 50px;
    -webkit-border-radius: 9px;
    -moz-border-radius: 9px;
    border-radius: 9px;
    padding: 0 20px;
    color: #111111;
    font-size: 1rem;
    font-family: 'precioussanstwo-medium'; 
    font-weight: 500;
    border: none;
    outline: none;
}



.form_wrap ul li textarea.form_main {
    -webkit-border-radius: 9px;
    -moz-border-radius: 9px;
    border-radius: 9px;
    padding: 15px 20px;
    line-height: 25px;
    height: 190px;
    font-family: 'precioussanstwo-medium'; 
}

:global(body.rtl) .form_main, :global(body.rtl) .form_wrap ul li textarea.form_main{
    font-family: var(--font-arabic);
}

.contact_btn {
    width: max-content;
    margin-left: auto;
    margin-right: 0;
}

.accept_content {
    width: 50%;
    padding-left: 20px;
}

.accept_content label {
    font-size: 1rem;
    color: #fff;
}

.accept_content input {
    margin-right: 10px;
    width: 15px;
    height: 15px;
    border: 1px solid #999999;
    position: relative;
    top: 1px;
    outline: none;
}

.contact_icon {
    width: 90px;
    height: 90px;
    background-color: #E8593B;
    -webkit-border-radius: 100%;
    -moz-border-radius: 100%;
    border-radius: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
    margin-bottom: 20px;
}

.contact_details ul {
    display: flex;
    flex-wrap: wrap;
}

.contact_details ul li {
    width: 33.3%;
    display: flex;
    flex-direction: column;
    flex-wrap: wrap;
    /*justify-content: center;*/
    align-items: center;
    padding: 15px 20px;
}

.contact_details ul li h4 {
    width: 100%;
    text-align: center;
    color: #D7D7D7;
    /* font-family: 'precioussanstwo-medium'; */
    font-size: 1rem;
    line-height: 22px;
    font-weight: 500;
}

.contact_details ul li h4:hover a {
    color: var(--color-primary);
}

.contact_details ul li p {
    text-align: center;
    color: #D7D7D7;
}

.contact_details ul li+li {
    border-left: 1px solid rgba(255, 255, 255, 0.2);
}

.contact_details_ar ul {
    display: flex;
    flex-wrap: wrap;
}

.contact_details_ar ul li {
    width: 33.3%;
    display: flex;
    flex-direction: column;
    flex-wrap: wrap;
    /*justify-content: center;*/
    align-items: center;
    padding: 15px 20px;
}

.contact_details_ar ul li h4 {
    width: 100%;
    text-align: center;
    color: #D7D7D7;
    /* font-family: 'precioussanstwo-medium'; */
    font-size: 1rem;
    line-height: 22px;
    font-weight: 500;
}

.contact_details_ar ul li h4:hover a {
    color: var(--color-primary);
}

.contact_details_ar ul li p {
    text-align: center;
    color: #D7D7D7;
}

.contact_details_ar ul li+li {
    border-right: 1px solid rgba(255, 255, 255, 0.2);
}



/* -------------------------------------------------------------------- */

.custom_checkbox {
    display: flex;
    align-items: center;
    font-size: 1rem;
    cursor: pointer;
}

.custom_checkbox input[type="checkbox"] {
    display: none;
}

.custom_checkbox .checkmark {
    width: 20px;
    height: 20px;
    background-color: #eee;
    border-radius: 4px;
    margin-right: 10px;
    position: relative;
    transition: background-color 0.2s ease;
}

.custom_checkbox .checkmark_ar {
    width: 20px;
    height: 20px;
    background-color: #eee;
    border-radius: 4px;
    margin-left: 10px;
    position: relative;
    transition: background-color 0.2s ease;
}

.custom_checkbox input[type="checkbox"]:checked+.checkmark {
    background-color: var(--color-primary);
    background-image: url(../../public/images/tick-icon.svg);
    background-repeat: no-repeat;
    background-size: 16px;
    background-position: top 1.2px center;
}

.custom_checkbox input[type="checkbox"]:checked+.checkmark::after {
    display: block;
}

.custom_checkbox input[type="checkbox"]:checked+.checkmark_ar {
    background-color: var(--color-primary);
    background-image: url(../../public/images/tick-icon.svg);
    background-repeat: no-repeat;
    background-size: 16px;
    background-position: top 1.2px center;
}

.custom_checkbox input[type="checkbox"]:checked+.checkmark_ar::after {
    display: block;
}

:global(body.rtl) .contact_btn {
    width: max-content;
    margin-right: auto;
    margin-left: 0;
}

@media (max-width: 860px) {
    .form_main {
        height: 44px;
        line-height: 44px;
    }

    .form_wrap ul li textarea.form_main {
        height: 120px;
    }

    .contact_icon {
        width: 70px;
        height: 70px;
        padding: 15px;
        margin-bottom: 15px;
    }
}

@media (max-width: 600px) {
    .form_wrap ul li {
        width: 100%;
    }

    .contact_icon {
        margin:0;
    }

    .contact_details ul li {
        width: 90%;
        margin: auto;
        gap: 20px;
        padding: 20px 0;
        flex-flow: row wrap;
        justify-content: flex-start;
    }

    .contact_details ul li+li {
        border: none;
        border-top: 1px solid rgba(255, 255, 255, 0.2);
    }

    .contact_details ul li h4 {
        width: auto;
    }

    .contact_details ul li p {
        text-align: left;
    }

    .contact_details_ar ul li {
        width: 90%;
        margin: auto;
        gap: 20px;
        padding: 20px 0;
        flex-flow: row wrap;
        justify-content: flex-start;
    }

    .contact_details_ar ul li+li {
        border: none;
        border-top: 1px solid rgba(255, 255, 255, 0.2);
    }

    .contact_details_ar ul li h4 {
        width: auto;
    }

    .contact_details_ar ul li p {
        text-align: right;
    }
}