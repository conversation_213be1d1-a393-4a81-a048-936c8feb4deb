.profile_wrap {
    width: 100%;
    height: auto;
}

.profile_image {
    width: 100%;
    overflow: hidden;
    -webkit-border-radius: 30px;
    -moz-border-radius: 30px;
    border-radius: 30px;
}

.profile_image img {
    width: 100%;
    height: 360px;
    object-fit: cover;
    object-position: top center;
}

.profile_content {
    width: 100%;
    padding-top: 15px;
}

.profile_content h3 {
    font-size: 18px;
    line-height: 24px;
    color: #fff;
    font-family: 'precioussanstwo-medium';
    font-weight: 600;
    margin-bottom: 5px;
}

:global(body.rtl) .profile_content h3{
    font-family: var(--font-arabic);
}

.profile_content p {
    color: #D7D7D7;
}

@media (max-width: 860px) {
    .profile_image {
        -webkit-border-radius: 15px;
        -moz-border-radius: 15px;
        border-radius: 15px;
    }
}