export default async function handler(req, res) {
    if (req.method !== 'POST') {
      return res.status(405).json({ message: 'Method Not Allowed' });
    }
  
    const { 'g-recaptcha-response': token } = Object.fromEntries(req.body.entries());
    const secretKey ='6LdsYQArAAAAAAnFclH5kT2bDYVfSHZtDfMuvHFu';
  
    const verifyURL = `https://www.google.com/recaptcha/api/siteverify?secret=6LdsYQArAAAAAAnFclH5kT2bDYVfSHZtDfMuvHFu&response=${token}`;
    
    const response = await fetch(verifyURL, { method: 'POST' });
    const data = await response.json();
  
    if (data.success) {
      return res.status(200).json({ success: true });
    } else {
      return res.status(400).json({ success: false, errors: data['error-codes'] });
    }
  }
  