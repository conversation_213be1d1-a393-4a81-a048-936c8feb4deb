import React from "react";
import styles from "./corporategovernance.module.css";
import Pagemenu from "@/components/pagemenu/pagemenu";
import SubBanner from "@/components/innerbanner/innerbanner";
import Profile from "@/components/profile/profile";
import { fetchPageBySlug } from "@/lib/api/PageBySlug";
import { fetchaboutTab } from "@/lib/api/byfield"
import Head from "next/head";
const menuItems = [
	{ name: "About Us", link: "/about-us" },
	{ name: "Mission, Vision & Values", link: "/about-us/#mission_vision" },
	{ name: "History", link: "/history" },
	{ name: "Tassnief Roles", link: "/tassnief-roles" },
	{ name: "Corporate Governance", link: "/corporate-governance" },
	{ name: "Regulatory Reports", link: "/regulatory-reports" },
];

const administrativeData = [
	{
		image: "/images/leadership_img1.jpg",
		title: "<PERSON>",
		description: "Chairman",
	},
	{
		image: "/images/leadership_img2.jpg",
		title: "<PERSON>",
		description: "Independent member",
	},
	{
		image: "/images/leadership_img3.jpg",
		title: "<PERSON><PERSON><PERSON>",
		description: "Non-Executive member",
	},
	{
		image: "/images/leadership_img4.jpg",
		title: "Mishal Al-<PERSON>",
		description: "Independent member",
	},
];

const Index = ({ pageData, Tabs }) => {
	const banner = pageData.acf.banner_details || '';
	const sec1 = pageData.acf.team_section_1 || '';
	const sec2 = pageData.acf.team_section_2 || '';
	const sec3 = pageData.acf.team_section_3 || '';
	const sec4 = pageData.acf.team_section_4 || '';
	return (
		<>


			<SubBanner
				bannerDetails={banner}
				alignEnd={false}
			/>
			<Pagemenu menuItems={Tabs.tabs} />

			<section className={`${styles.first_section} py-120 bg-secondary`}>
				<div className={styles.administrative}>
					<div className={`${styles.administrative_wrap} container`}>
						<div className="title text-white mb-50" data-aos="fade-up">
							<h2 className="main_title mb-20"
								dangerouslySetInnerHTML={{ __html: sec1.title }}
							/>
							<div dangerouslySetInnerHTML={{ __html: sec1.description }} />
						</div>
						<div className={`${styles.profile_list} text-white text-center`}>
							<Profile profiles={sec1.teams} />
						</div>
					</div>
				</div>
				<div className={styles.board}>
					<div className="container">
						<div className="text-white mb-50" data-aos="fade-up">
							<h2 className="main_title mb-20" dangerouslySetInnerHTML={{ __html: sec2.title }} />
							<div dangerouslySetInnerHTML={{ __html: sec2.description }} />
						</div>
						<div className={`${styles.profile_list} text-white text-center`}>
							<Profile profiles={sec2.teams} />
						</div>
					</div>
				</div>
			</section>

			<section className={`${styles.section_second} section_second`}>
				<div className="container">
					<div className="mb-50" data-aos="fade-up">
						<h2 className="main_title text-secondary mb-20" dangerouslySetInnerHTML={{ __html: sec3.title }} />
						<div dangerouslySetInnerHTML={{ __html: sec3.description }} />
					</div>
					<div className={`${styles.profile_list} text-center`}>
						<Profile profiles={sec3.teams} />
					</div>
				</div>
			</section>
			<section className="pt-120 bg-secondary">
				<div className="container">
					<div className="mb-50" data-aos="fade-up">
						<h2 className="main_title text-white mb-20" dangerouslySetInnerHTML={{ __html: sec4.title }} />
						<div className="color-pera" dangerouslySetInnerHTML={{ __html: sec4.description }} />
					</div>
					<div className={`${styles.profile_list} text-center text-white`}>
						<Profile profiles={sec4.teams} />
					</div>
				</div>
			</section>
		</>
	);
};

export default Index;

export async function getStaticProps({ locale }) {
	const langCode = locale === "ar" ? "ar" : "en";
	try {
		const pageData = await fetchPageBySlug("corporate-governance", langCode);
		const Tabs = await fetchaboutTab(langCode);

		return {
			props: {
				pageData,
				Tabs,
			},
			revalidate: 10,
		};
	} catch (error) {
		console.error("Failed to fetch data:", error);
		return {
			props: {
				pageData: null,
				Tabs: null,
			},
		};
	}
}
