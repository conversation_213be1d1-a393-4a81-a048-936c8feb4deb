import React from "react";
import { useState } from "react";
import Link from "next/link";
import Image from "next/image";
import styles from "./understaing.module.css";
import SubBanner from "@/components/innerbanner/innerbanner";
import Pagemenu from "@/components/pagemenu/pagemenu";
import Sixcolumn from "@/components/sixcolumn/sixcolumn";
import Threecolumn from "@/components/threecolumn/threecolumn";
import Membercards from "@/components/membercards/membercards";
import styles2 from "@/pages/services/service.module.css";
import { useRouter } from "next/router";

import Modal from "react-modal";
import { fetchPageBySlug } from "@/lib/api/PageBySlug";
import {fetchRatingTab} from "@/lib/api/byfield"


const Index = ({pageData,Tabs}) => {
	const { locale } = useRouter(); 
	const langCode = locale === 'ar' ? 'ar' : 'en';
	
	const [modalIsOpen, setIsOpen] = React.useState(false);

	function openModal() {
		setIsOpen(true);
	}

	function closeModal() {
		setIsOpen(false);
	}

	const banner = pageData.acf.banner_details || '';
	const data = pageData.acf.details_credit_rating || '';
	const boxs = pageData.acf.details_boxes || '';
	const credit = pageData.acf.details_cre || '';

	return (
    <>
      <SubBanner bannerDetails={banner} alignEnd={false} />
      <Pagemenu menuItems={Tabs.tabs} />

      <section
        className={`${styles.first_section} py-120 bg-secondary center_bg_2`}
      >
        <div className="container">
          <div className="text-center text-white mb-50" data-aos="fade-up">
            <h2
              className="main_title mb-20"
              dangerouslySetInnerHTML={{ __html: data.title_2 }}
            />
            <p
              className="color-pera"
              dangerouslySetInnerHTML={{ __html: data.description }}
            />
            <Link
              href={data.button.url}
              className={`${styles.btn_style_wrap} btn_style_wrap`}
            >
              <span className="btn_style_primary">{data.button.title}</span>
              <span
                className={`${
                  langCode === "ar" ? "btn_style_arrow_ar" : ""
                } ${"btn_style_arrow"}`}
              >
                <Image
                  src={
                    langCode == "ar"
                      ? "/images/white_arrow_left.png"
                      : "/images/white_right_arw.svg"
                  }
                  alt="icon"
                  width={22}
                  height={15}
                  priority
                />
              </span>
            </Link>
          </div>
          <div
            className={styles.video_block}
            data-aos="fade-up"
            style={{ marginBottom: "5%" }}
          >
            <Image
              src="/images/video_bg.jpg"
              alt="image"
              width={1360}
              height={422}
              priority
            />
            <div className={styles.play_btn} onClick={openModal}>
              <Image
                src="/images/polygon.svg"
                alt="arrow"
                width={25}
                height={25}
                priority
              />
            </div>
          </div>
          <Sixcolumn
            className={`${styles2.sixcolumn} `}
            sixColumnItems={boxs.cards}
            text_size="text_size"
          />
        </div>
      </section>
      <section className="py-120 bg-light">
        <div className="container">
          <h2
            className="text-center text-secondary main_title mb-50"
            data-aos="fade-up"
            dangerouslySetInnerHTML={{ __html: credit.title }}
          />
          <Threecolumn columns={credit.cards} />
        </div>
      </section>
      <section className="pt-120 bg-secondary">
        <div className="container">
          <Membercards />
        </div>
      </section>

      <Modal
        isOpen={modalIsOpen}
        onRequestClose={closeModal}
        contentLabel="Example Modal"
        className="modelStyles"
      >
        <span className="closeButton closeButton_new" onClick={closeModal}>
          X
        </span>
        <video
          width="320"
          height="240"
          className={styles.banner_video}
          autoPlay
          loop
          muted
          playsInline
          controls
        >
          <source src={data.video} type="video/mp4" />
          <source src={data.video} type="video/ogg" />
          Your browser does not support the video tag.
        </video>
      </Modal>
    </>
  );
};

export default Index;

export async function getServerSideProps({locale}) {
	const langCode = locale === "ar" ? "ar" : "en";
	try {
		const pageData = await fetchPageBySlug("understanding-credit-rating", langCode);
		const Tabs = await fetchRatingTab(langCode);
		return {
			props: {
				pageData,
				Tabs
			},
		};
	} catch (error) {
		console.error("Failed to fetch data:", error);
		return {
			props: {
				pageData: null
			},
		};
	}
}