import React, { useState, useEffect } from "react";
import Link from "next/link";
import Image from "next/image";
import styles from "./membercards.module.css";
import { AllOptions } from "@/lib/api/Options";
import { useRouter } from 'next/router';

const Membercards = () => {
	const { locale } = useRouter();
	//console.log("Current locale:", locale); 
	const langCode = locale === 'ar' ? 'ar' : 'en';
	const [lc, setLc] = useState(locale);
	const [options, setOptions] = useState(null);

	useEffect(() => {
		const fetchOptions = async () => {
			try {
				const res = await fetch(
					`${process.env.NEXT_PUBLIC_API_BASE_URL}/options/all?lang=${langCode}`
				);
				const data = await res.json();
				setOptions(data);
			} catch (error) {
				console.error("Error fetching options:", error);
			}
		};

		fetchOptions();
	}, [locale]);

	if (!options) return null; 

	return (
    <div className={styles.membercard_list}>
      {/* Member Card */}
      <div
        className={`${
          langCode === "ar" ? styles.membercard_item_ar : styles.membercard_item
        }`}
        style={{ "--image": `url('/images/pattern1.png')` }}
        data-aos="fade-right"
      >
        <h3>{options.member_card?.title}</h3>
        <div className={styles.membercard_body}>
          {options.member_card?.image && (
            <Image
              src={options.member_card.image}
              alt="image"
              width={136}
                loading='lazy'
              height={49}
            />
          )}
          <p dangerouslySetInnerHTML={{ __html: options.member_card?.description }}/>
        </div>
        <Link
          href={options.member_card?.button.url || "#"}
          className={`${styles.btn_style_wrap} btn_style_wrap`}
        >
          <span className="btn_style_primary">
            {options.member_card?.button.title}
          </span>
          <span
            className={`${styles.btn_style_arrow} ${
              langCode === "ar" ? "btn_style_arrow_ar" : ""
            } ${"btn_style_arrow"}`}
          >
            <Image
              src={
                langCode === "ar"
                  ? "/images/white_arrow_left.png"
                  : "/images/white_right_arw.svg"
              }
              alt="icon"
              width={22}
              height={15}
              priority
            />
          </span>
        </Link>
      </div>

      {/* Rating Request */}
      <div
        className={`${
          langCode === "ar" ? styles.membercard_item_ar : styles.membercard_item
        }`}
        style={{ "--image": `url('/images/pattern2.png')` }}
        data-aos="fade-left"
      >
        <h3>{options.rating_request?.title}</h3>
        <div className={styles.membercard_body}>
          {options.rating_request?.image && (
            <Image
              src={options.rating_request.image}
              alt="image"
                loading='lazy'
              width={136}
              height={49}
            />
          )}
          <p dangerouslySetInnerHTML={{ __html: options.rating_request?.description }} />
        </div>
        <Link
          href={options.rating_request?.button.url || "#"}
          className={`${styles.btn_style_wrap} btn_style_wrap`}
        >
          <span className="btn_style_primary">
            {options.rating_request?.button.title}
          </span>
          <span
            className={`${styles.btn_style_arrow} ${
              langCode === "ar" ? "btn_style_arrow_ar" : ""
            } ${"btn_style_arrow"}`}
          >
            <Image
              src={
                langCode === "ar"
                  ? "/images/white_arrow_left.png"
                  : "/images/white_right_arw.svg"
              }
              alt="icon"
              width={22}
              height={15}
              priority
            />
          </span>
        </Link>
      </div>
    </div>
  );
};

export default Membercards;
