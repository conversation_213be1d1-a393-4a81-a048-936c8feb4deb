import React from "react";
import Pagemenu from "@/components/pagemenu/pagemenu";
import SubBanner from "@/components/innerbanner/innerbanner";
import Historyslider from "@/components/historyslider/historyslider";
import { fetchPageBySlug } from "@/lib/api/PageBySlug";
import { fetchaboutTab } from "@/lib/api/byfield";
import Head from "next/head";

const Index = ({ pageData, Tabs }) => {
	const banner = pageData.acf.banner || "";

	return (
		<>
			
			<SubBanner bannerDetails={banner} alignEnd={false} />

			<Pagemenu menuItems={Tabs.tabs} />

			<section className="py-120 bg-secondary center_bg">
				<div className="container">
					<div
						className="title text-center text-white mb-50"
						data-aos="fade-up"
					>
						<h2
							className="main_title mb-20"
							dangerouslySetInnerHTML={{ __html: pageData.acf.title_his }}
						/>
						<p
							className="color-pera"
							dangerouslySetInnerHTML={{ __html: pageData.acf.description_his }}
						/>
					</div>
					<Historyslider HistoryList={pageData.acf.history_list} />
				</div>
			</section>
		</>
	);
};

export default Index;
export async function getServerSideProps({ locale }) {
	const langCode = locale === "ar" ? "ar" : "en";
	try {
		const pageData = await fetchPageBySlug("history", langCode);
		const Tabs = await fetchaboutTab(langCode);
		return {
			props: {
				pageData,
				Tabs,
			},
		};
	} catch (error) {
		console.error("Failed to fetch data:", error);
		return {
			props: {
				pageData: null,
			},
		};
	}
}
