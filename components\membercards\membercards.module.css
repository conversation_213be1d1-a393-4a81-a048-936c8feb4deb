.membercard_list {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    row-gap: 20px;
    column-gap: 30px;
}

.membercard_item {
    display: flex;
    flex-flow: column wrap;
    color: #ffffff;
    row-gap: 22px;
    padding: 60px 70px;
    overflow: hidden;
    background: var(--color-gradient);
    position: relative;
    border-radius: var(--radius-30);
}

.membercard_item:nth-child(1):after {
    right: 0;
    width: 392px;
    height: 555px;
}

.membercard_item::after {
    content: "";
    position: absolute;
    width: 280px;
    height: 250px;
    right: 0;
    bottom: -10px;
    background-image: var(--image);
    background-repeat: no-repeat;
    background-position: right bottom;
}

.membercard_body {
    display: flex;
    align-items: center;
    column-gap: 30px;
}

.membercard_item h3 {
    font-size: 2.8125rem;
    line-height: 43px;
    letter-spacing: -0.01em;
    font-weight: 400;
    line-height: 1;
    font-family: 'precioussanstwo-bold';
}



.membercard_item p {
    margin: 0;
}

.membercard_item_ar {
    display: flex;
    flex-flow: column wrap;
    color: #ffffff;
    row-gap: 22px;
    padding: 60px 70px;
    overflow: hidden;
    background: var(--color-gradient-ar);
    position: relative;
    border-radius: var(--radius-30);
}

.membercard_item_ar:nth-child(1):after {
    left: 0;
    width: 392px;
    height: 555px;
}

.membercard_item_ar::after {
    content: "";
    position: absolute;
    width: 280px;
    height: 250px;
    left: 0;
    bottom: -10px;
    background-image: var(--image);
    background-repeat: no-repeat;
    background-position: left bottom;
}

.membercard_item_ar h3 {
    font-size: 2.8125rem;
    line-height: 43px;
    letter-spacing: -0.01em;
    font-weight: 400;
    line-height: 1;
    font-family: 'precioussanstwo-bold';
}

:global(body.rtl) .membercard_item_ar h3 {
    font-family: inherit !important;
}

.membercard_item_ar p {
    margin: 0;
}

@media (max-width: 1200px) {
    .membercard_item h3 {
        font-size: 2.25rem;
    }

    .membercard_item {
        padding: 40px;
    }

    .membercard_item_ar h3 {
        font-size: 2.25rem;
    }

    .membercard_item_ar {
        padding: 40px;
    }
}

@media (max-width: 1040px) {
    .membercard_body {
        column-gap: 15px;
    }

    .membercard_item h3 {
        font-size: 2rem;
    }

    .membercard_item {
        row-gap: 14px;
    }

    .membercard_item {
        padding: 30px;
    }

    .membercard_item_ar h3 {
        font-size: 2rem;
    }

    .membercard_item_ar {
        row-gap: 14px;
    }

    .membercard_item_ar {
        padding: 30px;
    }
}

@media (max-width: 860px) {
    .membercard_list {
        column-gap: 15px;
    }

    .membercard_item::after {
        width: 180px;
        height: 150px;
        background-size: contain;
    }

    .membercard_item:nth-child(1):after {
        width: 292px;
        height: 455px;
    }

    .membercard_item_ar::after {
        width: 180px;
        height: 150px;
        background-size: contain;
    }

    .membercard_item_ar:nth-child(1):after {
        width: 292px;
        height: 455px;
    }
}

@media (max-width: 767px) {
    .membercard_list {
        grid-template-columns: repeat(1, 1fr);
    }

    .membercard_item:nth-child(1):after {
        width: 235px;
    }

    .membercard_item_ar:nth-child(1):after {
        width: 235px;
    }
}