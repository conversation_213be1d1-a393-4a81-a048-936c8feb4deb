// components/CustomSelect.js
import React, { useState, useRef } from 'react';
import styles from './select.module.css'; // Assuming you have some styles in SCSS

const SelectStyle = ({ options, label, placeholder = "Select an option", onChange, reset, defaultToFirst  }) => {
    const [isOpen, setIsOpen] = useState(false);
    const [selectedOption, setSelectedOption] = useState(null); // Start with no selected option (null)
    const selectRef = useRef(null);

    const handleSelect = (option) => {
        setSelectedOption(option);
        setIsOpen(false);
        onChange(option);
    };

    const toggleDropdown = () => {
        setIsOpen(!isOpen);
    };

    const handleClickOutside = (event) => {
        if (selectRef.current && !selectRef.current.contains(event.target)) {
            setIsOpen(false);
        }
    };

    // Close the dropdown when clicking outside
    React.useEffect(() => {
        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, []);
    React.useEffect(() => {
        if (reset) {
            setSelectedOption(null);
        }
    }, [reset]);

     // Default to first option if flag is true
    React.useEffect(() => {
        if (defaultToFirst && options?.length > 0 && !selectedOption) {
            setSelectedOption(options[0]);
            onChange(options[0]);
        }
    }, [defaultToFirst, options]);

    return (
        <div className={`${styles.customSelect} customSelect`} ref={selectRef} >
            <div className={`${styles.selected} selected`} onClick={toggleDropdown} style={{overflow:'hidden'}}>
                {selectedOption ? selectedOption.label : <span className={styles.placeholder}>{placeholder}</span>}
            </div>
            {isOpen && (
                <ul className={`${styles.dropdown} dropdown`}
                style={{maxHeight:'340px', overflowY:'scroll'}}
                >
                    {options.map((option) => (
                        
                        <li
                            value={option.value}
                            key={option.value}
                            onClick={() => handleSelect(option)}
                            className={styles.dropdownItem}
                            style={{width:'100%'}}
                        >
                            {option.label}
                        </li>
                    ))}
                </ul>
            )}
        </div>
    );
};

export default SelectStyle;
