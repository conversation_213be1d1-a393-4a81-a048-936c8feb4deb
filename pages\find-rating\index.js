import React, { useEffect, useRef, useState } from "react";
import Link from "next/link";
import Image from "next/image";
import styles from "./findRatings.module.css";
import Pagemenu from "@/components/pagemenu/pagemenu";
import SubBanner from "@/components/innerbanner/innerbanner";
import SelectStyle from "@/components/SelectBox/select";
import Membercards from "@/components/membercards/membercards";
import { fetchPageBySlug, fetchRatings, fetchServices } from "@/lib/api/PageBySlug";
import {fetchRatingTab} from "@/lib/api/byfield"
import Head from "next/head";
const options = [
	{ value: "option1", label: "Option 1" },
	{ value: "option2", label: "Option 2" },
	{ value: "option3", label: "Option 3" },
	{ value: "option4", label: "Option 4" },
	{ value: "option5", label: "Option 5" },
];
import { useRouter } from "next/router";

const Index = ({pageData, Tabs, ratingList, serviceList}) => {
	
	const { locale } = useRouter();
	const langCode = locale === 'ar' ? 'ar' : 'en';
	
	const banner = pageData.acf.banner_details || '';
	const [selectedOption, setSelectedOption] = useState(null);
	const [names, setNames] = useState([]);
	const [nameFilter, setNameFilter] = useState();

	const [rating, setRating] = useState([]);
	const [ratingv, setRatingv] = useState();

	const [ratingType, setRatingType] = useState([]);
	const [ratingTypev, setRatingTypev] = useState();

	const [ratingActions, setRatingActions] = useState([]);
	const [ratingActionsv, setRatingActionsv] = useState();

	const [outlooks, setOutlooks] = useState([]);
	const [outlooksv, setOutlooksv] = useState();

	const [years, setYears] = useState();
	const resultsDivRef = useRef(null);

	//console.log(ratingList)

	useEffect(() => {
		if (ratingList) {
			// Unique names
			const uniqueOptions = ratingList.reduce((acc, item) => {
				const entity_name = item.title.rendered.split(' ').slice(0, 10).join(' ');; 
				const existingType = acc.find(option => option.value === entity_name); 

				if (!existingType) {
					acc.push({ value: entity_name, label: entity_name });
				}
				return acc;
			}, []);
			uniqueOptions.unshift({ value: "", label: "Choose" });
			setNames(uniqueOptions);
			
			// Unique ratings
			const ratings = ratingList.reduce((acc, item) => {
				const ratingValue = item.acf.rating_.value; 
				const existingType = acc.find(option => option.value === ratingValue); 

				if (!existingType) {
					acc.push({ value: ratingValue, label: item.acf.rating_.label });
				}
				return acc;
			}, []);
			ratings.unshift({ value: "", label: "Choose" });
			setRating(ratings);
			
			// Unique rating actions
			const actions = ratingList.reduce((acc, item) => {
				const ratingActionValue = item.acf.rating_action.value; 
				const existingType = acc.find(option => option.value === ratingActionValue); 

				if (!existingType) {
					acc.push({ value: ratingActionValue, label: item.acf.rating_action.label });
				}
				return acc;
			}, []);
			actions.unshift({ value: "", label: "Choose" });
			setRatingActions(actions);

			//Unique Type
			const Type = serviceList.reduce((acc, item) => {
				const ratingActionValue1 = item.title.rendered; 
				const existingType = acc.find(option => option.value === ratingActionValue1); 
				acc.push({ value: item.title.rendered, label: item.title.rendered});
				// if (!existingType) {
				// 	acc.push({ value: item.acf.rating_type.post_title, label: item.acf.rating_type.post_title});
				// }
				return acc;
			}, []);
			Type.unshift({ value: "", label: "Choose" });
			setRatingType(Type);
			
			// Unique outlooks
			const outlooks = ratingList.reduce((acc, item) => {
				const outlookValue = item.acf.outlook.value; 
				const existingType = acc.find(option => option.value === outlookValue); 

				if (!existingType) {
					acc.push({ value: outlookValue, label: item.acf.outlook.label });
				}
				return acc;
			}, []);
			outlooks.unshift({ value: "", label: "Choose" });
			setOutlooks(outlooks);
			
			
		}
	}, [pageData.acf.rating_list_d]);

	
	const [filteredResults, setFilteredResults] = useState(ratingList);

	const handleFilter = (event) => {
		event.preventDefault(); 
	  
		const results = ratingList.filter(item => {
			const isYearMatching = new Date(item.acf.issue_date).getFullYear(); // 2023
			//console.log(isYearMatching);
		  return (
			(!nameFilter || item.title.rendered === nameFilter) &&
			(!ratingv || item.acf.rating_.value === ratingv) &&
			(!ratingTypev || item.acf.rating_type.post_title === ratingTypev) &&
			(!ratingActionsv || item.acf.rating_action.value === ratingActionsv) &&
			(!outlooksv || item.acf.outlook.value === outlooksv) &&
			(!years || years == isYearMatching)
			
		  );
		});
		setCurrentPage(1);
		setFilteredResults(results);
		resultsDivRef.current.scrollIntoView({ behavior: "smooth" });
	  };
	  
	  const [currentPage, setCurrentPage] = useState(1);
      const itemsPerPage = 10;
	  const indexOfLastItem = currentPage * itemsPerPage;
	  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
	  const currentItems = filteredResults.slice(indexOfFirstItem, indexOfLastItem);
	
	  const totalPages = Math.ceil(filteredResults.length / itemsPerPage);

	  useEffect(() => {
		if (currentPage > totalPages) {
		  setCurrentPage(totalPages);
		}
	  }, [filteredResults, totalPages, currentPage]);
	
	  const handlePageChange = (pageNumber) => {
		setCurrentPage(pageNumber);
	  };
	
	  const pageNumbers = [];
	  for (let i = 1; i <= totalPages; i++) {
		pageNumbers.push(i);
	  }




	return (
		<>
			
			<SubBanner
				bannerDetails={banner}
				alignEnd={false}
			/>

			<Pagemenu menuItems={Tabs.tabs} />

			<section className="pt-120 bg-secondary center_bg_2">
				<div className={styles.first_block}>
					<div className="container">
						<h2 className="main_title text-center text-white mb-20" data-aos="fade-up">
							{pageData.acf.details_table.title}
						</h2>
						<div className={styles.first_content} data-aos="fade-up">
							<form className={styles.first_form}>
								<ul className={styles.first_form_list}>
									<li>
										<label>{pageData.acf.details_table.table_titles[0].title}</label>
										<SelectStyle
											options={names}
											label="Choose an option"
											placeholder={locale === 'ar' ? 'اختر' : 'Choose'}
											onChange={(option) => { 
												setNameFilter(option.value); 
											}}
										/>
									</li>
									<li>
										<label>{pageData.acf.details_table.table_titles[2].title}</label>
										<SelectStyle
											options={rating}
											label="Choose an option"
											placeholder={locale === 'ar' ? 'اختر' : 'Choose'}
											onChange={(option) => { 
												setRatingv(option.value); 
											}}
										/>
									</li>
									<li>
										<label>{pageData.acf.details_table.table_titles[3].title}</label>
										<SelectStyle
											options={ratingType}
											label="Choose an option"
											placeholder={locale === 'ar' ? 'اختر' : 'Choose'}
											onChange={(option) => { 
												setRatingTypev(option.value); 
											}}
										/>
									</li>
									<li>
										<label>{pageData.acf.details_table.table_titles[4].title}</label>
										<SelectStyle
											options={ratingActions}
											label="Choose an option"
											placeholder={locale === 'ar' ? 'اختر' : 'Choose'}
											onChange={(option) => { 
												setRatingActionsv(option.value); 
											}}
										/>
									</li>
									<li>
										<label>{pageData.acf.details_table.table_titles[5].title}</label>
										<SelectStyle
											options={outlooks}
											label="Choose an option"
											placeholder={locale === 'ar' ? 'اختر' : 'Choose'}
											onChange={(option) => { 
												setOutlooksv(option.value); 
											}}
										/>
									</li>
									<li>
										<label>{locale === 'ar' ? "سنة الإصدار" : "Issued Year"}</label>
										<input type="text" 
										 onChange={(event) => { 
											setYears(event.target.value); 
										  }}
										/>
									</li>
									<li ref={resultsDivRef}>
										<button
											className={`${styles.btn_style_wrap} btn_style_wrap`}
											onClick={(event) => handleFilter(event)}
										>
											<span className={`${langCode === "ar" ? "btn_style_primary_ar" : ""} btn_style_primary`}>Find a Rating</span>
											<span  className={`${langCode === "ar" ? "btn_style_arrow_ar" : ""} ${"btn_style_arrow"}`}>
												<Image
													src={
														langCode == "ar"
														  ? "/images/white_arrow_left.png"
														  : "/images/white_right_arw.svg"
													}
													alt="icon"
													width={22}
													height={15}
													priority
												/>
											</span>
										</button>
									</li>
								</ul>
							</form>
						</div>
					</div>
				</div>
				<div className="py-120">
					<div className="container" >
						<div className={styles.table_style_second} data-aos="fade-up">
						{currentItems.length > 0 ? (
							<table className={styles.main_table_wrap}>
								<thead>
									<tr>
									{pageData.acf.details_table.table_titles.map((item, index) => {
										return (
											<th key={index} align={index <= 2 ? "left" : undefined}>
												{item.title}
											</th>
										);
									})}

										
									</tr>
								</thead>								
									<tbody>
									{currentItems.map((item,index) => {
										return (
											<tr key={index}>		
											<td dangerouslySetInnerHTML={{ __html: item.title.rendered }} />
											<td>{item.acf.rating_type.post_title}</td>											
											<td>{item.acf.rating_.label}</td>											
											<td>{item.acf.rating_action.label}</td>
											<td>{item.acf.outlook.label}</td>
											<td>{item.acf.issue_date}</td>
											
											{/* <td>{item.acf.exp_date}</td> */}
											{item.acf.press_release_.url && (
											<td style={{textAlign:'center'}}>
												
													<a
														href={item.acf.press_release_.url}
														className={`${styles.pdf_link} text-primary`}
														download={item.acf.press_release_.filename}
														target="_blank"
														style={{justifyContent:'center'}}
													>
														{/* PDF */}
														<Image
															src="/images/dowload-primary.svg"
															alt={`icon`}
															width={15}
															height={15}
															priority
														/>
													</a>
												
											</td>
											)}
											<td style={{textAlign:'center'}}>
												{item.acf.report.url && (
													<a
														href={item.acf.report.url}
														className={`${styles.pdf_link} text-primary`}
														download={item.acf.report.filename}
														target="_blank"
														style={{justifyContent:'center'}}
													>
														{/* PDF */}
														<Image
															src="/images/dowload-primary.svg"
															alt={`icon`}
															width={15}
															height={15}
															priority
														/>
													</a>
												)}
											</td>
										</tr>
										)
									})}
									
								</tbody>
								
							</table>):(
								<>
									<p style={{textAlign:'center',width:'100%',color:'white'}}> No Data Found</p>
								</>
							)}
						</div>

						{totalPages > 1 && (
							<div
								style={{
								width: "100%",
								display: "flex",
								gap: "10px 20px",
								justifyContent: "flex-end",
								paddingRight: "25px",
								marginTop:'20px'
								}}
							>
								{pageNumbers.map((number) => (
								<button
									key={number}
									onClick={() => handlePageChange(number)}
									className={currentPage === number ? `activePage` : ""}
									style={{
									background: "transparent",
									color: currentPage === number ? "white" : "#787878",
									border: "unset",
									}}
								>
									{number}
								</button>
								))}
							</div>
							)}
					</div>
				</div>
				<div className="container">
					<Membercards />
				</div>
			</section>
		</>
	);
};

export default Index;

export async function getServerSideProps({locale}) {
	const langCode = locale === "ar" ? "ar" : "en";
	try {
		const pageData = await fetchPageBySlug("find-a-rating", langCode);
		const Tabs = await fetchRatingTab(langCode);
		const ratingList = await fetchRatings(langCode,100)
		const serviceList = await fetchServices(langCode);
		return {
			props: {
				pageData,
				Tabs,
				ratingList,
				serviceList
			},
		};
	} catch (error) {
		console.error("Failed to fetch data:", error);
		return {
			props: {
				pageData: null
			},
		};
	}
}