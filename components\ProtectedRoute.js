import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/router';

const ProtectedRoute = ({ children }) => {
    const router = useRouter();
    const [isAuthenticated, setIsAuthenticated] = useState(false);
    const { locale } = useRouter();
	// console.log(options)
	useEffect(() => {
	  if (locale === 'ar') {
		// document.body.classList.add('rtl');
		document.body.classList.remove("ltr", "english-cls");
		document.body.classList.add("rtl", "arabic-cls");
		document.body.setAttribute("dir", "rtl");
	  } else {
		// document.body.classList.remove('rtl'); 
		document.body.classList.remove("rtl", "arabic-cls");
		document.body.classList.add("ltr", "english-cls");
		document.body.setAttribute("dir", "");
	  }
	}, [locale]);

    useEffect(() => {
        const token = localStorage.getItem('token');
        if (token) {
            setIsAuthenticated(true); // User is authenticated
        } else {
            router.push('/login'); // Redirect if no token
        }
    }, [router]);

    if (!isAuthenticated) {
        return <div className={` py-120 bg-secondary`}></div>;
    }

    return <>{children}</>;
};

export default ProtectedRoute;
