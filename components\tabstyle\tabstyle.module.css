.tab_head {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 16px;
}

.tab_head_item {
    height: 49px;
    padding: 0 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ffffff;
    cursor: pointer;
    border: 1px solid var(--color-primary);
    border-radius: 9px;
}

.tab_head_item:not(.react-tabs__tab--selected) {
    background-color: var(--color-primary);
}

.tab_body {
    padding-top: 50px;
}