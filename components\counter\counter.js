import React from "react";
import CountUp, { useCountUp } from "react-countup";
import styles from "./counter.module.css";
import { useRouter } from "next/router";

function Counter({ count, langcode }) {
  const { locale } = useRouter();
  useCountUp({
    ref: "counter",
    enableScrollSpy: true,
    scrollSpyDelay: 1000,
  });
  return (
    <div
      className={langcode === "en" ? styles.counter_list : styles.counter_list_ar}
    >
      {count.map((counter, index) => (
        <div
          className={
            langcode === "en" ? styles.counter_item : styles.counter_item_ar
          }
          data-aos="fade-up"
          data-aos-delay={counter.delay}
          key={index}
        >
          <p className={langcode === "en" ? styles.counter : styles.counter_ar}
            style={{ fontFamily: 'precioussanstwo-medium' }}>
            {counter.prefix && <span className={styles.special_char} >{counter.prefix}</span>}
            <CountUp end={counter.number} enableScrollSpy />
            {counter.suffix && <span className={styles.special_char}>{counter.suffix}</span>}
          </p>
          <p>{counter.title}</p>
        </div>
      ))}
    </div>
  );
}

export default Counter;
