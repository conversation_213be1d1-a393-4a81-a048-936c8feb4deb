.threecolumn_list {
    row-gap: var(--gap-18);
    justify-content: space-between;
}

.threecolumn_item {
    --size: 125px;
    gap: var(--gap-30);
    width: calc((100% / 3) - var(--gap-18));
    padding: 80px 25px;
    border-radius: var(--radius-34);
    position: relative;
    backdrop-filter: blur(50px);
    z-index: 0;
}

.threecolumn_item::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(113.49deg,
            rgba(232, 89, 59, 1) 0%,
            rgba(0, 40, 85, 1) 100%);
    border-radius: var(--radius-30);
    opacity: 0;
    visibility: hidden;
    transition: opacity .4s ease-in-out, visibility .4s ease-in-out;
    z-index: -1;
}

.threecolumn_circle {
    width: var(--size);
    height: var(--size);
    background: var(--color-primary);
    border-radius: var(--radius-round);
    transition: all 0.3s ease-in-out;
}

.threecolumn_item h3 {
    font-size: 1.50rem;
    margin-bottom: 20px;
    color: var(--color-secondary);
    
    @media (max-width:500px){
        margin-bottom: 10px;

    }
}

.threecolumn_item p {
    color: #111111;
}

/* --------------------------------------------- */

.threecolumn_item:hover::after {
    opacity: 1;
    visibility: visible;
}

.threecolumn_item:hover .threecolumn_circle {
    background: #ffffff;
}

.threecolumn_item:hover .threecolumn_circle img {
    filter: invert(42%) sepia(48%) saturate(765%) hue-rotate(323deg) brightness(109%) contrast(93%);
}

.threecolumn_item:hover p {
    color: #ffffff;
}

.threecolumn_item:hover h3 {
    color: #ffffff;
}

@media (max-width: 1200px) {
    .threecolumn_item {
        --size: 100px;
        padding: 45px 15px;
    }

    .threecolumn_item img {
        transform: scale(0.8);
    }

    .threecolumn_item h4 {
        margin-bottom: 15px;
    }
}

@media (max-width: 860px) {
    .threecolumn_item {
        padding: 30px 15px;
    }
}

@media (max-width: 767px) {
    .threecolumn_item {
        width: 100%;
    }
}
