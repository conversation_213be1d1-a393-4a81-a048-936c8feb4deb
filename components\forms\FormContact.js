import React, { useRef } from "react";
import styles from "@/pages/contact-us/contact.module.css"
import { useState } from 'react';
import { useRouter } from 'next/router';
import Link from "next/link";
import Image from "next/image";
import ReCAPTCHA from "react-google-recaptcha";

const FormContact = () => {
	const { locale } = useRouter();
	const langCode = locale === 'ar' ? 'ar' : 'en';

  const siteKey = '6LdsYQArAAAAAP2LBpUnQmZOjwzho_nuAqiv6OBM';
  const recaptchaRef = useRef(null);
  
	const [formData1, setFormData] = useState({
		FirstName: '',
		LastName: '',
		EmailAddress: '',
		PhoneNumber: '',
		Message: '',
	});

	const [validationErrors, setValidationErrors] = useState({});
	const [isSubmitting, setIsSubmitting] = useState(false);
	const [responseMessage, setResponseMessage] = useState('');
	const [sresponseMessage, setSresponseMessage] = useState('');
	const [fieldErrors, setFieldErrors] = useState({});

    const [isChecked, setIsChecked] = useState(false); 
  
    const handleCheckboxChange = (event) => {
      setIsChecked(event.target.checked); 
    };

	
	const formId = locale === 'ar' ? 1395 : 8;
	const unitTag = locale === 'ar' ? 'wpcf7-f1395-o1' : 'wpcf7-f8-o1';
  //   const formId =  8;
	// const unitTag = 'wpcf7-f8-o1';

	const handleChange = (e) => {
		const { name, value } = e.target;
		setFormData({ ...formData1, [name]: value });
		setResponseMessage('');
		setValidationErrors({})
		setSresponseMessage('');
		if (name === "PhoneNumber") {
			const numericValue = value.replace(/\D/g, ""); 
			setFormData({ ...formData1, [name]: numericValue });
		  } 
		  if (name === "FirstName" || name == "LastName") {
			const alphabeticValue = value.replace(/[^a-zA-Z]/g, "");
			setFormData({ ...formData1, [name]: alphabeticValue });
		} 
	};

	const handleSubmit = async (e) => {
		e.preventDefault();
		setIsSubmitting(true);
		setResponseMessage('');

		// Validate form data
		// if (!validateForm()) {
		// 	setIsSubmitting(false);
		// 	return;
		// }

		// Create a new FormData object
		const formData = new FormData();
		formData.append('FirstName', formData1.FirstName);
		formData.append('LastName', formData1.LastName);
		formData.append('EmailAddress', formData1.EmailAddress);
		formData.append('PhoneNumber', formData1.PhoneNumber);
		formData.append('Message', formData1.Message);
		formData.append("_wpcf7_unit_tag", unitTag);

		try {
			const response = await fetch(`${process.env.NEXT_PUBLIC_CONTACT_API_URL}/${formId}/feedback`, {
				method: 'POST',
				body: formData,
				redirect: 'follow',
			});
		
			const result = await response.json();
			setIsSubmitting(false);
		
			if (response.ok) {
				if (result.status === "validation_failed") {
					const fieldErrors = result.invalid_fields.reduce((acc, fieldError) => {
						acc[fieldError.field] = fieldError.message;
						return acc;
					}, {});
					setFieldErrors(fieldErrors);
					// setResponseMessage('Error in fields');

					setTimeout(() => {
						setResponseMessage('');
						setFieldErrors({});
					  }, 3000);
					

				} else if (result.status === "mail_sent") {
					setSresponseMessage(result.message);
					setTimeout(() => {
						setSresponseMessage('');
					}, 3000);
					setFieldErrors({}); 
					setFormData({ FirstName: '', LastName: '', EmailAddress: '', PhoneNumber: '', Message: '' });
				} else {
					setResponseMessage('An unexpected error occurred. Please try again.');
					setTimeout(() => {
						setFieldErrors({});
					  }, 3000);
				}
				
			} else {
				setResponseMessage(result.message || 'Something went wrong. Please try again.');
				setTimeout(() => {
					setFieldErrors({});
				  }, 3000);
			}
		} catch (error) {
			console.error('Error:', error);
			setIsSubmitting(false);
			setResponseMessage('An error occurred while submitting the form.');
			setTimeout(() => {
				setResponseMessage({});
			  }, 3000);
		}
	};
  
  return (
    <>
      <form className="contact_form" onSubmit={handleSubmit}>
        <ul>
          <li>
            <input
              name="FirstName"
              placeholder={langCode === "ar" ? "الاسم الأول*" : "First Name*"}
              className={styles.form_main}
              value={formData1.FirstName}
              onChange={handleChange}
              required
            />
            {fieldErrors.FirstName && (
              <p
                style={{
                  color: "red",
                  marginTop: "5px",
                  fontSize: "12px",
                  padding: "0 8px",
                }}
              >
                {fieldErrors.FirstName}
              </p>
            )}
          </li>
          <li>
            <input
              name="LastName"
              placeholder={langCode === "ar" ? "اسم العائلة*" : "Last Name*"}
              className={styles.form_main}
              value={formData1.LastName}
              onChange={handleChange}
              required
            />

            {fieldErrors.LastName && (
              <p
                style={{
                  color: "red",
                  marginTop: "5px",
                  fontSize: "12px",
                  padding: "0 8px",
                }}
              >
                {fieldErrors.LastName}
              </p>
            )}
          </li>
          <li>
            <input
              name="EmailAddress"
              type="email"
              placeholder={
                langCode === "ar"
                  ? "عنوان البريد الإلكتروني"
                  : "Email Address"
              }
              className={styles.form_main}
              value={formData1.EmailAddress}
              onChange={handleChange}
              
            />

            {fieldErrors.EmailAddress && (
              <p
                style={{
                  color: "red",
                  marginTop: "5px",
                  fontSize: "12px",
                  padding: "0 8px",
                }}
              >
                {fieldErrors.EmailAddress}
              </p>
            )}
          </li>
          <li>
            <input
              name="PhoneNumber"
              type="text"
              placeholder={langCode === "ar" ? "رقم الهاتف*" : "Phone Number*"}
              className={styles.form_main}
              value={formData1.PhoneNumber}
              onChange={handleChange}
              required
              minLength={9}
              maxLength={15}
            />
            {fieldErrors.PhoneNumber && (
              <p
                style={{
                  color: "red",
                  marginTop: "5px",
                  fontSize: "12px",
                  padding: "0 8px",
                }}
              >
                {fieldErrors.PhoneNumber}
              </p>
            )}
          </li>
          <li className={styles.full_width}>
            <textarea
              maxLength="500"
              name="Message"
              placeholder={langCode === "ar" ? "الرسالة... " : "Message... "}
              className={styles.form_main}
              value={formData1.Message}
              onChange={handleChange}
              
            ></textarea>

            {fieldErrors.Message && (
              <p
                style={{
                  color: "red",
                  marginTop: "5px",
                  fontSize: "12px",
                  padding: "0 8px",
                }}
              >
                {fieldErrors.Message}
              </p>
            )}
          </li>
          <li className={styles.full_width}>
            <div className={styles.accept_content}>
              <label className={styles.custom_checkbox}>
                <input type="checkbox" onChange={handleCheckboxChange} />
                <span
                  className={
                    langCode === "ar" ? styles.checkmark_ar : styles.checkmark
                  }
                ></span>
                {locale === 'ar' ? "أوافق على الشروط" : "I accept the Terms"}
              </label>
            </div>

            <ReCAPTCHA ref={recaptchaRef} sitekey={siteKey} size="invisible" />

            {isSubmitting ? (
              <button
                href="#."
                className={`${styles.contact_btn} btn_style_wrap`}
              >
                <span className="btn_style_primary">...</span>
                {/* <span className="btn_style_arrow">
													<Image
														alt="icon"
														width="22"
														height="15"
														src="/images/white_right_arw.svg"
													/>
												</span> */}
              </button>
            ) : (
              <button
                href="#."
                className={`${styles.contact_btn} btn_style_wrap`}
                style={{
                  pointerEvents: !isChecked ? "none" : "auto",
                  opacity: !isChecked ? 0.5 : 1,
                }}
              >
                <span className="btn_style_primary">Submit</span>
                <span
                  className={`${
                    langCode === "ar" ? "btn_style_arrow_ar" : ""
                  } ${"btn_style_arrow"}`}
                >
                  <Image
                    alt="icon"
                    width="22"
                loading='lazy'
                    height="15"
                    src={
                      langCode === "ar"
                        ? "/images/white_arrow_left.png"
                        : "/images/white_right_arw.svg"
                    }
                  />
                </span>
              </button>
            )}
          </li>
        </ul>
        {responseMessage && (
          <p style={{ color: "red", fontSize: "12px" }}>{responseMessage}</p>
        )}
        {sresponseMessage && (
          <p style={{ color: "green", fontSize: "12px" }}>{sresponseMessage}</p>
        )}
      </form>
    </>
  );

}

export default FormContact
