import React from 'react'
import styles from "@/pages/tassnief-roles/tassniefroles.module.css";
import SubBanner from "@/components/innerbanner/innerbanner";
import { fetchPageBySlug } from "@/lib/api/PageBySlug";

const Index = ({pageData}) => {
	
	const banner = pageData.acf.banner_details || '';
  return (
    <>
      		<SubBanner
				bannerDetails={banner}
				alignEnd={false}
			/>
			<section className="py-120 bg-secondary">
				<div className="container">
					<div className="text-white mb-50" data-aos="fade-up">
						{/* <h2 className="main_title mb-20"
							dangerouslySetInnerHTML={{ __html: details.title }}
						/> */}
						<div className="color-pera"
							dangerouslySetInnerHTML={{ __html: pageData.acf.description_policy }}
						/>
					</div>
				</div>
			</section>

    </>
  )
}

export default Index

export async function getServerSideProps({ locale }) {
	const langCode = locale === "ar" ? "ar" : "en";
	//console.log(langCode)
	try {
		const pageData = await fetchPageBySlug("privacy-policy", langCode);

		return {
			props: {
				pageData,
			},
		};
	} catch (error) {
		console.error("Failed to fetch data:", error);
		return {
			props: {
				pageData: null,
			},
		};
	}
}
