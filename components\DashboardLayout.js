import React, { useEffect, useState } from "react";
import Link from "next/link";
import styles from "@/pages/dashboard/myaccount.module.css";
import Sidemenu from "@/components/sidemenu/sidemenu";
import Dashboard from "@/components/dashboard/dashboard";
import ProtectedRoute from "@/components/ProtectedRoute";
import { fetchPageBySlug } from "@/lib/api/PageBySlug";
import Head from "next/head";
import Header from "@/components/header/Header";
import Footer from "@/components/footer/Footer";
import { useRouter } from "next/router";
const Index = ({ children }) => {
	const [pageData, setPageData] = useState(null);
	const { locale } = useRouter();
	// console.log('yyyyyy',pageData)
	useEffect(() => {
	  if (locale === 'ar') {
		// document.body.classList.add('rtl');
		document.body.classList.remove("ltr", "english-cls");
		document.body.classList.add("rtl", "arabic-cls");
		document.body.setAttribute("dir", "rtl");
	  } else {
		// document.body.classList.remove('rtl'); 
		document.body.classList.remove("rtl", "arabic-cls");
		document.body.classList.add("ltr", "english-cls");
		document.body.setAttribute("dir", "");
	  }
	}, [locale]);

	useEffect(() => {
		const fetchData = async () => {
			try {
				const data = await fetchPageBySlug("dashboard",locale);
				setPageData(data);
			} catch (error) {
				console.error("Failed to fetch data:", error);
			}
		};

		fetchData();
	}, []);

	// if (!pageData) {
	// 	return <div></div>;
	// }

	return (
        <>
        
            <Header>
				<Head>
					<title>Tassneif</title>
					<meta name="description" content="" />
					<meta name="viewport" content="width=device-width, initial-scale=1" />
					<link rel="icon" href="/favicon.ico" />
					<link rel = "stylesheet" href ="../styles/arabic.css" />
				</Head>
		    </Header>
            <ProtectedRoute>
			<div className={`${styles.my_account} py-120 bg-secondary`}>
				<div className="container">
                    {pageData && (
                        <h2 className={`${styles.title} main_title text-white text-center`}>
                            {pageData.acf.title}
                        </h2>
                    )}
					
					<div className={styles.container_inside}>
						<div className={styles.left_navigation}>
							{pageData && (<h4>{pageData.acf.sidemenu.title}</h4> )}
							<Sidemenu />
						</div>
						<div className={styles.my_account_right}>
							{children }
						</div>
					</div>
				</div>
			</div>
		</ProtectedRoute>
        <Footer></Footer>
        </>
		
	);
};

export default Index;
