body {
  --space-50: 50px;
  --space-80: 80px;
  --space-100: 100px;
  --space-110: 110px;
  --space-120: 120px;
}

.status-paid {
  color: #37A312 !important;
}

.status-pending {
  color: #208CCE !important;
}

.status-cancelled {
  color: #f2f2f2 !important;
}

.gap-5 {
  gap: 5px;
}

.gap-10 {
  gap: 10px;
}

.gap-15 {
  gap: 15px;
}

.gap-20 {
  gap: 20px;
}

/* -------------------------------------------------------------- */

.mb-15 {
  margin-bottom: 15px;
}

.mb-20 {
  margin-bottom: 20px;
}

.mt-20 {
  margin-top: 20px;
}

.mb-30 {
  margin-bottom: 30px;
}

.mt-30 {
  margin-top: 30px;
}

.mt-50 {
  margin-top: var(--space-50)
}

.mb-50 {
  margin-bottom: var(--space-50)
}

.mb-50 {
  margin-bottom: 50px;
}

.mt-100 {
  margin-top: var(--space-100);
}

.mb-100 {
  margin-bottom: var(--space-100);
}

/* -------------------------------------------------------------- */

.pt-50 {
  padding-top: var(--space-50)
}

.pb-50 {
  padding-bottom: var(--space-50)
}


.pb-80 {
  padding-bottom: var(--space-80)
}

.pt-80 {
  padding-top: var(--space-80)
}

.py-80 {
  padding-bottom: var(--space-80);
  padding-top: var(--space-80);
}

.pb-90 {
  padding-bottom: var(--space-90)
}

.pt-90 {
  padding-top: var(--space-90)
}

.py-90 {
  padding-bottom: var(--space-90);
  padding-top: var(--space-90);
}

.pt-100 {
  padding-top: var(--space-100)
}

.pb-100 {
  padding-bottom: var(--space-100)
}

.py-100 {
  padding-top: var(--space-100);
  padding-bottom: var(--space-100);
}

.pt-110 {
  padding-top: var(--space-110)
}

.pb-110 {
  padding-bottom: var(--space-110)
}

.py-110 {
  padding-top: var(--space-110);
  padding-bottom: var(--space-110);
}

.py-120 {
  padding-top: var(--space-120);
  padding-bottom: var(--space-120);
}

.pt-120 {
  padding-top: var(--space-120);
}

.pb-120 {
  padding-bottom: var(--space-120);
}

/* -------------------------------------------------------------- */

.text-left {
  text-align: left;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.text-uppercase {
  text-transform: uppercase;
}

.text-capitalize {
  text-transform: capitalize;
}

/* -------------------------------------------------------------- */

.color-pera {
  color: #D7D7D7;
}

.text-white {
  color: #ffffff;
}

.text-primary {
  color: var(--color-primary);
}

.text-secondary {
  color: var(--color-secondary);
}

.bg-dimgray {
  background: #F2F2F2;
}

/* -------------------------------------------------------------- */

.font-primary {
  font-family: var(--font-primary);
}

.font-secondary {
  font-family: var(--font-secondary);
}

/* -------------------------------------------------------------- */

.bg-gray {
  background: #404041;
}

.bg-light {
  background: var(--color-light);
}

.bg-white {
  background: #ffffff;
}

.bg-primary {
  background: var(--color-primary);
}

.bg-secondary {
  background: var(--color-secondary);
}

/* -------------------------------------------------------------- */

.m-auto {
  margin-left: auto;
  margin-right: auto;
}

.ml-auto {
  margin-left: auto;
}

.mr-auto {
  margin-right: auto;
}

/* -------------------------------------------------------------- */

.d-flex-row,
.d-flex-column,
.d-flex-center {
  display: flex;
}

.d-flex-row {
  flex-flow: wrap row;
}

.d-flex-column {
  flex-flow: wrap column;
}

.d-flex-center {
  align-items: center;
  justify-content: center;
}

.d-flex-ycenter {
  align-items: center;
}

.d-flex-xbetween {
  justify-content: space-between;
}

.d-flex-xcenter {
  justify-content: center;
}

.d-flex-align-end {
  align-items: flex-end;
}

.d-flex-justify-end {
  justify-content: flex-end;
}

/* -------------------------------------------------------------- */

@media (max-width: 1200px) {
  body {
    --gap-24: 16px;
    --space-50: 50px;
    --space-80: 80px;
    --space-100: 50px;
    --space-110: 50px;
    --space-120: 80px;
  }

  .mb-50 {
    margin-bottom: 30px;
  }

  .home-news .swiper-button-next,
  .home-news .swiper-button-prev {
    top: -78px;
  }
}

@media (max-width: 860px) {
  body {
    --space-50: 50px;
    --space-80: 50px;
    --space-100: 50px;
    --space-110: 50px;
    --space-120: 50px;
  }
}
@media (max-width: 767px){
  .rating_sevices .d-flex-center{
    flex-wrap:wrap;
    justify-content: center;
    row-gap:15px;
    text-align: center;
  }
  .rating_sevices .d-flex-center > div{
    width: 100%;
  }
}
