import React from "react";
import SubBanner from "@/components/innerbanner/innerbanner";
import Pagemenu from "@/components/pagemenu/pagemenu";
import { fetchPageBySlug } from "@/lib/api/PageBySlug";
import {fetchRatingTab} from "@/lib/api/byfield"
import CreditRatingChart from "@/components/ratingchart/CreditRatingChart";


const Index = ({pageData,Tabs}) => {
	const banner = pageData.acf.banner_details || '';
	return (
		<>
			<SubBanner
				bannerDetails={banner}
				alignEnd={false}
			/>
            <Pagemenu menuItems={Tabs.tabs} />
			<section className="pt-120 bg-secondary center_bg">
				<div className="container">
					<CreditRatingChart />
				</div>
			</section>
		</>
	);
};

export default Index;
export async function getServerSideProps({locale}) {
	const langCode = locale === "ar" ? "ar" : "en";
	try {
		const pageData = await fetchPageBySlug("rating-scale", langCode);
		const Tabs = await fetchRatingTab(langCode);
		return {
			props: {
				pageData,
				Tabs
			},
		};
	} catch (error) {
		console.error("Failed to fetch data:", error);
		return {
			props: {
				pageData: null
			},
		};
	}
}
