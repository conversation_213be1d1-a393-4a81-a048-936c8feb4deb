import { useState, useEffect } from "react";
import Link from "next/link";
import Image from "next/image";
import styles from "./footer.module.css";
import { AllOptions } from "@/lib/api/Options";
import { useRouter } from 'next/router';
import Newsletter from "../forms/NewsLetter";

const Footer = ({ login }) => {
  const { locale } = useRouter();
  const langCode = locale === "ar" ? "ar" : "en";
  const [lc, setLc] = useState(locale);
  const [options, setOptions] = useState(null);

  useEffect(() => {
    const fetchOptions = async () => {
      try {
        const res = await fetch(
          `${process.env.NEXT_PUBLIC_API_BASE_URL}/options/all?lang=${langCode}`
        );
        const data = await res.json();
        setOptions(data);
      } catch (error) {
        console.error("Error fetching options:", error);
      }
    };

    fetchOptions();
  }, [locale]);
  if (!options) return null;
  return (
    <footer
      className={`${styles.footer} bg-secondary`}
      style={{
        backgroundPosition: login ? "center" : "bottom center",marginTop:'-0.2px'
      }}
    >
      <div className="container">
        <div className={`${styles.footer_top} footer_top`} data-aos="fade-up">
          <div className={styles.footer_logo}>
            <Image
              src={options.footer.logo}
              alt="logo"
              width={137}
                loading='lazy'
              height={66}
            />
          </div>
          <div className={styles.footer_content}>
            <div className={styles.footer_content_left}>
              <h4>{options.footer.quick_links.title}</h4>
              <ul>
                {options.footer.quick_links.footer_menu.map((item, index) => {
                  return (
                    <li key={index}>
                      <Link href={item.menu.url}>{item.menu.title}</Link>
                    </li>
                  );
                })}
              </ul>
            </div>
            <div className={styles.footer_content_right}>
              <h4>{options.footer.contact.title}</h4>
              <Link
                className={styles.footer_phone}
                href={`tel:${options.footer.contact.phone}`}
              >
                {options.footer.contact.phone}
              </Link>
              <Link
                className={styles.footer_email}
                href={`mailto:${options.footer.contact.email}`}
              >
                {options.footer.contact.email}
              </Link>
              <div
              className="textP"
                dangerouslySetInnerHTML={{
                  __html: options.footer.contact.address,
                }}
              />
            </div>
          </div>
        </div>
        

        <div className={styles.footer_bottom}>
          <div className={styles.newsletter}>
            <h3
              dangerouslySetInnerHTML={{
                __html: options.footer.newsleter.ndewsletter_title,
              }}
            />
            <div className={styles.newsletter_right}>
              <div className={styles.newsletter_content}>
                <p
                  dangerouslySetInnerHTML={{
                    __html: options.footer.newsleter.label,
                  }}
                />
                <Newsletter />
              </div>
              <ul className="social_icons">
                {options.footer.social_media_links.facebook && (
                  <li>
                    <Link href={options.footer.social_media_links.facebook}>
                      <Image
                        src="/images/fb.svg"
                        alt="facebook"
                        width={10}
                loading='lazy'
                        height={19}
                      />
                    </Link>
                  </li>
                )}
                
                {options.footer.social_media_links.linkedin && (
                  <li>
                    <Link href={options.footer.social_media_links.linkedin}>
                      <Image
                        src="/images/in.svg"
                        alt="linkedin"
                        width={18}
                loading='lazy'
                        height={17}
                      />
                    </Link>
                  </li>
                )}

                {options.footer.social_media_links.x && (
                  <li>
                    <Link href={options.footer.social_media_links.x}>
                      <Image
                        src="/images/x.svg"
                        alt="x"
                        width={17}
                loading='lazy'
                        height={15}
                      />
                    </Link>
                  </li>
                )}

                {options.footer.social_media_links.instagram && (
                  <li>
                    <Link href={options.footer.social_media_links.instagram}>
                      <Image
                        src="/images/ins.svg"
                        alt="instagram"
                        width={19}
                loading='lazy'
                        height={19}
                      />
                    </Link>
                  </li>
                )}
              </ul>

            </div>
          </div>
        </div>
        <div className={styles.footer_copyright}>
          <p dangerouslySetInnerHTML={{ __html: options.footer.copyright }} />
          <ul
            className={langCode === "ar" ? styles.privacy_terms_ar : styles.privacy_terms }
          >
            <li>
              <Link href={options.footer.policy.url || "#"}>
                {options.footer.policy.title}
              </Link>
            </li>
            <li>
              <Link href={options.footer.terms_conditions.url || "#."}>
                {options.footer.terms_conditions.title}
              </Link>
            </li>
          </ul>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
