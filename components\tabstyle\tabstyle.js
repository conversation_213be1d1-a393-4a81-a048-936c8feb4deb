import React from "react";
import styles from "./tabstyle.module.css";
import { Tab, Tabs, Tab<PERSON>ist, TabPanel } from "react-tabs";
import Threecolumn from "../threecolumn/threecolumn";


const Tabstyle = ({tabList}) => {
	return (
		<>
			<Tabs>
				<TabList className={styles.tab_head}>
					<Tab className={styles.tab_head_item}>{tabList.title_1}</Tab>
					<Tab className={styles.tab_head_item}>{tabList.title_2}</Tab>
					<Tab className={styles.tab_head_item}>{tabList.title_3}</Tab>
				</TabList>

				<TabPanel className={styles.tab_body}>
					<Threecolumn columns={tabList.cards_1} />
				</TabPanel>
				<TabPanel>
					<Threecolumn columns={tabList.cards_2} />
				</TabPanel>
				<TabPanel>
					<Threecolumn columns={tabList.cards_3} />
				</TabPanel>
			</Tabs>
		</>
	);
};

export default Tabstyle;
