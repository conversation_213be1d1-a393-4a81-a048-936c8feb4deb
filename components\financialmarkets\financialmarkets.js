import React from "react";
import Image from "next/image";
import styles from "./financialmarkets.module.css";


const Financialmarkets = ({list}) => {
	return (
		<div className={styles.financialmarkets}>
			{list.map((item, index) => (
				<div key={index} className={styles.financialmarkets_item}>
					<div className={styles.financialmarkets_icon}>
						<Image
							src={item.icon}
							alt={item.title} // Use a meaningful alt text
							width={46}
                loading='lazy'
							height={40}
						/>
					</div>
					<div className={styles.financialmarkets_content}>
						<h4 dangerouslySetInnerHTML={{ __html: item.title }} />
						<p dangerouslySetInnerHTML={{ __html: item.description }} />
					</div>
				</div>
			))}
		</div>
	);
};

export default Financialmarkets;
