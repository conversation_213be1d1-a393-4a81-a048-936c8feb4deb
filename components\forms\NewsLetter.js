import React, { useState, useRef } from "react";
import styles from "@/components/footer/footer.module.css";
import { useRouter } from 'next/router';
import Image from "next/image";
import ReCAPTCHA from "react-google-recaptcha";

const Newsletter = () => {
  const { locale } = useRouter();
  const langCode = locale === 'ar' ? 'ar' : 'en';

  const siteKey = '6LdsYQArAAAAAP2LBpUnQmZOjwzho_nuAqiv6OBM';
  const recaptchaRef = useRef(null);

  const [email, setEmail] = useState('');
  const [message, setMessage] = useState('');
  const [emessage, setEmessage] = useState('');

  const messages = {
    en: {
      success: 'Thank You! You are successfully subscribed to get our latest updates.',
      error: 'An error occurred. Please try again.',
      placeholder: 'YOUR EMAIL',
      submit: 'Subscribe',
    },
    ar: {
      success: 'شكراً لك! لقد تم الاشتراك بنجاح لتلقي أحدث تحديثاتنا.',
      error: 'حدث خطأ ما. يرجى المحاولة مرة أخرى.',
      placeholder: 'عنوان البريد الإلكتروني',
      submit: 'اشترك',
    },
  };

  const handleSubmit = async (event) => {
    event.preventDefault(); // Prevent the default form submission

    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/subscribers?lang=${langCode}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });

      const data = await response.json();

      if (response.ok) {
        setEmessage('');
        setMessage(messages[langCode].success); 
        setEmail('');
        setTimeout(() => {
          setMessage('');
        }, 3000);
      } else {
        setMessage('');
        setEmail('');
        setEmessage(data.message || messages[langCode].error);
        setTimeout(() => {
          setEmessage('');
        }, 3000);
      }
    } catch (error) {
      setMessage('');
      setEmessage(messages[langCode].error);
      setTimeout(() => {
        setEmessage('');
      }, 3000);
    }
  };

  return (
    <>
      <form onSubmit={handleSubmit} className={styles.newsletter_form}>
        <input
          className={`${styles.input_field} input_field`}
          type="email"
          placeholder={messages[langCode].placeholder}
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          required
          style={{ textTransform: "unset" }}
        />
        {/* <input
          className={`${styles.submit} submit`}
          type="submit"
          value={messages[langCode].submit}
        /> */}

<ReCAPTCHA ref={recaptchaRef} sitekey={siteKey} size="invisible" />

        <button type="submit">
          <Image
            src={
              langCode === "ar"
                ? "/images/newsletter_arw_right.png"
                : "/images/newsletter_arw.svg"
            }
            alt="send"
            width={15}
                loading='lazy'
            height={8}
          />
        </button>
      </form>
      {message && (
        <p
          style={{
            color: "green",
            paddingTop: "5px",
            textAlign: "center",
            fontSize: "12px",
            width: "100%",
          }}
        >
          {message}
        </p>
      )}
      {emessage && (
        <p
          style={{
            color: "red",
            paddingTop: "5px",
            textAlign: "center",
            fontSize: "12px",
            width: "100%",
          }}
        >
          {emessage}
        </p>
      )}
    </>
  );
};

export default Newsletter;
