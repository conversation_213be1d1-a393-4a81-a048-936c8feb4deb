import React from "react";
import Link from "next/link";
import Image from "next/image";
import styles from "./about.module.css";
import SubBanner from "@/components/innerbanner/innerbanner";
import Sixcolumn from "@/components/sixcolumn/sixcolumn";
import Threecolumn from "@/components/threecolumn/threecolumn";
import Profile from "@/components/profile/profile";
import Pagemenu from "@/components/pagemenu/pagemenu";
import { fetchPageBySlug } from "@/lib/api/PageBySlug";
import Head from "next/head";

const Index = ({ pageData }) => {
	const banner = pageData.acf.banner_details || "";
	const tabList = pageData.acf.tabs_list || "";
	const about = pageData.acf.details_about || "";
	const value = pageData.acf.details_values || "";
	const message = pageData.acf.message_from_chairman || "";
	const team = pageData.acf.team || "";
	return (
		<>
			
			<SubBanner bannerDetails={banner} alignEnd={false} />
			<Pagemenu menuItems={pageData.acf.tabs_list.tabs} />

			<section className={`${styles.about_first} py-120 bg-secondary center_bg`}>
				<div className={`${styles.about_first_wrap} container`}>
					<div className={`${styles.about_first_content} text-white`}>
						<div className={styles.about_first_left} data-aos="fade-right">
							<h2>
								<span dangerouslySetInnerHTML={{ __html: about.sub_title }} />
								<b dangerouslySetInnerHTML={{ __html: about.title_2 }} />
							</h2>
						</div>
						<div className={styles.about_first_right} data-aos="fade-left">
							<p
								className="color-pera"
								dangerouslySetInnerHTML={{ __html: about.description }}
							/>
						</div>
					</div>
					<div id="mission_vision">
						<Sixcolumn
							className={styles.sixcolumn}
							sixColumnItems={about.mission_vision}
						/>
					</div>
				</div>
			</section>
			<section className="py-120 bg-light">
				<div className="container">
					<h2
						className="text-center text-secondary main_title mb-50"
						data-aos="fade-up"
						dangerouslySetInnerHTML={{ __html: value.title }}
					/>
					<Threecolumn columns={value.cards} />
				</div>
			</section>
			<section className={`${styles.about_last} py-120 bg-secondary`}>
				<div className={`${styles.about_last_wrap} container`}>
					<div className={styles.about_massage}>
						<div className={styles.about_massage_left} data-aos="fade-right">
							<Image
								width={536}
								height={649}
								src={message.image}
								alt="massage" loading='lazy'
							/>
						</div>
						<div className={styles.about_massage_right} data-aos="fade-left">
							<h2
								className="main_title text-white mb-20"
								dangerouslySetInnerHTML={{ __html: message.title }}
							/>
							<div dangerouslySetInnerHTML={{ __html: message.message }} />
						</div>
					</div>
					{/* <div className={styles.about_team}>
						<h2 className="main_title text-white mb-30" data-aos="fade-up">
							{pageData.acf.title_team}
						</h2>
						<div className={styles.about_team_list}>
							<Profile profiles={team} />
						</div>
					</div> */}
				</div>
			</section>
		</>
	);
};

export default Index;

export async function getStaticProps({ locale }) {
	const langCode = locale === "ar" ? "ar" : "en";
	//console.log(langCode)
	try {
		const pageData = await fetchPageBySlug("about-us", langCode);

		return {
			props: {
				pageData,
			},
			revalidate: 10, 
		};
	} catch (error) {
		console.error("Failed to fetch data:", error);
		return {
			props: {
				pageData: null,
			},
		};
	}
}
