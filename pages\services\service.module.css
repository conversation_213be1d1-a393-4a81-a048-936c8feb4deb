.service_page_wrap {
    width: 100%;
    padding: 80px 0 40px;
    position: relative;
}

.service_page_wrap::after {
    content: "";
    width: 100%;
    height: 100%;
    /* background-image: url(../../public/images/service-bg.png); */
    background-image: url(../../public/images/service-bg.webp);

    position: absolute;
    background-repeat: no-repeat;
    background-position: top center;
    top: 0;
    z-index: 0;
}

.sixcolumn {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--gap-24);
}

.sixcolumn .sixcolumn_item {
    padding: auto;
}

.service_page_wrap .relative {
    position: relative;
    z-index: 2;
}

@media (max-width: 860px) {
    .service_page_wrap {
        padding: 50px 0 40px;
    }
}

@media (max-width: 600px) {
    .sixcolumn {
        grid-template-columns: repeat(1, 1fr);
    }
}