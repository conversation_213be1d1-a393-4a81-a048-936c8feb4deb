import { useState, useEffect } from "react";
import Image from "next/image";
import Link from "next/link";
import styles from "./news.module.css";
import SubBanner from "@/components/innerbanner/innerbanner";
import Newscard from "@/components/newscard/newscard";
import { fetchPageBySlug, fetchNews } from "@/lib/api/PageBySlug";
import { fetchNONews } from "@/lib/api/byfield";
import Head from "next/head";

import { useRouter } from "next/router";

function News({ newsList, pageData, noText }) {
    const banner = pageData?.acf?.banner_details || '';
    const [latestPost, setLatestPost] = useState(null);
    const [latestThreePost, setLatestThreePost] = useState([]);
	const [latestRestPost, setLatestRestPost] = useState([]);

    useEffect(() => {
        if (newsList.length > 0) {
            setLatestPost(newsList[0]);
            setLatestThreePost(newsList.slice(1, 5)); 
			setLatestRestPost(newsList.slice(5));
        }
    }, [newsList]);

  const formatDate = (dateString, locale = "en") => {
    const date = new Date(dateString);
    const day = date.getDate();
    const month = date.toLocaleString(locale, { month: "long" });
    const year = date.getFullYear(); 

    return `${day}, ${month}, ${year}`;
  };

    const { locale } = useRouter(); 
    const langCode = locale === 'ar' ? 'ar' : 'en';

    return (
      <div>
        <SubBanner bannerDetails={banner} alignEnd={false} />
        <div className="bg_main">
          <section
            className={`${styles.news_top_section} pt-120 pb-50 bg-secondary`}
          >
            {newsList.length == 0 ? (
              <h3
                style={{
                  textAlign: "center",
                  color: "#a7a7a7",
                  fontWeight: "300",
                }}
              >
                {noText}
              </h3>
            ) : (
              <div className={`${styles.container} container relative flex`}>
                <div className={styles.news_top_left} data-aos="fade-right">
                  <span>{pageData.acf.news_page_titles.main}</span>
                  {latestPost && (
                    <Link href={`news/${latestPost.slug}`}>
                      <Image
                        src={latestPost.acf?.listing || "/fallback-image.jpg"}
                        alt="Latest News Image"
                        width={784}
                        height={564} loading='lazy'
                      />
                      <div
                        className={`${styles.news_main_title} news_main_title_ar`}
                      >
                        <label>{formatDate(latestPost.date, locale)}</label>
                        <h4
                          dangerouslySetInnerHTML={{
                            __html: latestPost.title.rendered,
                          }}
                        />
                      </div>
                    </Link>
                  )}
                </div>

                <div
                  className={`${styles.news_top_right} news_top_right_ar`}
                  data-aos="fade-left"
                >
                  <ul>
                    {latestThreePost.map((item, index) => (
                      <li key={index}>
                        <div className={styles.news_left_img}>
                          <Link href={`news/${item.slug}`}>
                            <Image
                              style={{
                                maxHeight: "172px",
                                width: "100%",
                                objectFit: "cover",
                              }}
                              src={item.acf?.listing || "/fallback-image.jpg"}
                              alt="image"
                              width={212}
                              height={172} loading='lazy'
                            />
                          </Link>
                        </div>
                        <div
                          className={`${styles.news_left_content} news_left_content_ar`}
                        >
                          <label>{formatDate(item.date, locale)}</label>
                          <h4>
                            <Link
                              href={`news/${item.slug}`}
                              dangerouslySetInnerHTML={{
                                __html: item.title.rendered,
                              }}
                            />
                          </h4>
                        </div>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            )}
          </section>
          {latestRestPost.length > 0 && (
            <section
              className={`${styles.news_list_section} pt-50 bg-secondary`}
            >
              <div className="container relative">
                <h2 className="main_title text-white mb-30">
                  {pageData.acf.news_page_titles.related}
                </h2>
                <div className={styles.news_list} data-aos="fade-up">
                  {latestRestPost.map((item) => {
                    return <Newscard key={item.id} item={item} />;
                  })}
                </div>
              </div>
            </section>
          )}
        </div>
      </div>
    );
}

export default News;

export async function getServerSideProps({locale}) {
	const langCode = locale === "ar" ? "ar" : "en";
    try {
        const pageData = await fetchPageBySlug("news", langCode);
        const newsList = await fetchNews(langCode,"100", );
        const noText = await fetchNONews(langCode );
        return {
            props: {
                pageData,
                newsList,
                noText
            },
        };
    } catch (error) {
        console.error("Failed to fetch data:", error);
        return {
            props: {
                pageData: null,
                newsList: [], 
            },
        };
    }
}
