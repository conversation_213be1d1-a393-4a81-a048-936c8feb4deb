export const fetchaboutTab = async (langCode) => {
    const response1 = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/page-field/about-us/tabs_list/${langCode}`);
  
    if (!response1.ok) {
      const errorMessage = `Error ${response1.status}: ${response1.statusText}`;
      throw new Error(errorMessage);
    }
  
    const data = await response1.json();
    return data;
  };
  export const fetchRatingTab = async (langCode) => {
    const response2 = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/page-field/rating/understanding-credit-rating/tabs_list/${langCode}`);
  
    if (!response2.ok) {
      const errorMessage = `Error ${response2.status}: ${response2.statusText}`;
      throw new Error(errorMessage);
    }
  
    const data = await response2.json();
    return data;
  };

  export const fetchSideMenu= async (langCode) => {
    const response3 = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/page-field/dashboard/sidemenu/${langCode}`);
  
    if (!response3.ok) {
      const errorMessage = `Error ${response3.status}: ${response3.statusText}`;
      throw new Error(errorMessage);
    }
  
    const data = await response3.json();
    return data;
  };

  export const fetchNONews = async (langCode) => {
    const response2 = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/page-field/news/no_news_text/${langCode}`);
  
    if (!response2.ok) {
      const errorMessage = `Error ${response2.status}: ${response2.statusText}`;
      throw new Error(errorMessage);
    }
  
    const data = await response2.json();
    return data;
  };