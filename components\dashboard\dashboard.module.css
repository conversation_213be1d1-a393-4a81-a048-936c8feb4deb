.card {
    display: flex;
    align-items: flex-end;
    min-height: 150px;
    background: #ffffff;
    padding: 30px 25px;
    width: calc(50% - 12.5px);
    border: 1px solid #A9A9A9;
    background-repeat: no-repeat;
    background-position: right 10px top 10px;
    border-radius: 14px;
}

.card h4 {
    font-size: 1.3125rem;
    color: var(--color-secondary);
}

.card p {
    color: #111111;
    margin-top: 10px;
}

:global(body.rtl) .card{
    background-position: left 10px top 10px;
}

@media (max-width: 600px) {
    .card {
        width:100%;
    }
}