.home_counter {
  overflow: hidden;
  position: relative;
  z-index: 0;
  /* background-image: url(../public/images/home_counter_bg.png); */
  background-image: url(../public/images/home_counter_bg.webp);
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
}

/* 
.home_counter::after {
    content: "";
    background: #e8593b;
    border-radius: 50%;
    opacity: 0.7;
    height: 637px;
    width: 637px;
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    margin: auto;
    filter: blur(367px);
    z-index: -1;
    will-change: transform, filter;
    -webkit-filter: blur(367px);
} */

.home_counter_content {
  color: #ffffff;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: flex-end;
  row-gap: 30px;
  margin-bottom: 100px;

  @media (max-width:700px) {

    row-gap: 10px;


  }
}

.home_counter_content_left,
.home_counter_content_right {
  width: 45%;
}

.home_counter_content_left h2 {
  font-size: 2.375rem;
  /* font-family: "precioussanstwo-medium"; */
  line-height: 51px;
  letter-spacing: -1px;
  font-weight: 600;
}

.home_counter_content_left span {
  font-size: 1.75rem;
  display: block;
  /* font-family: "precioussanstwo-thin"; */
  margin-bottom: 5px;
}

.home_counter_content_right p {
  color: #d7d7d7;
}

/* ------------------------------------------------ */

.home_ratings {
  overflow: hidden;
  background-image: url(../public/images/news_grdnt.webp);
  /* background-image: url(../public/images/news_grdnt.png); */
  /* background-image: url(../public/images/news_grdnt1.png); */
  background-repeat: no-repeat;
  background-position: top center;
}

.home_ratings,
.section_rating_sevices {
  position: relative;
  z-index: 0;
}

.home_ratings .container {
  position: relative;
  z-index: 2;
}

.rating_sevices {
  position: relative;
  margin-top: 400px;
}

.rating_sevices_ar {
  position: relative;
  margin-top: 400px;
}

.rating_sevices::after {
  content: "";
  width: 1600px;
  height: 2105px;
  background: url(../public/images/ratings_grd.png) no-repeat;
  position: absolute;
  top: -100%;
  left: -50%;
  margin: auto;
  z-index: -1;
}

.p_font_weight {
  font-weight: 600;
}

.how_to_help_box {
  width: 100%;
  position: fixed;
  bottom: 80px;
  right: -10%;
  z-index: 1;


}

.how_to_help_box a {
  display: inline-flex;
  gap: 8px;
  float: right;
}

.how_to_help_box_ar {
  width: 100%;
  position: fixed;
  bottom: 80px;
  /* left: 0px; */
  left: -10%;
  z-index: 1;

  @media (max-width: 1500px) {

    left: -7%;
  }

  @media (max-width: 1280px) {

    left: 0%;
  }
}

.how_to_help_box_ar a {
  display: inline-flex;
  gap: 8px;
  float: left !important;
  transition: all var(--time) ease;
}

.phone_box_container {
  transition: all var(--time) ease;
}

.phone_txt_box {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 213px;
  height: 44px;
  background: #1f2e58;
  border-radius: 37px;
  color: #fff;
  text-transform: uppercase;
  font-size: 14px;
  font-weight: 400;
  /* opacity: 0; */
  display: none;
  font-family: var(--font-primary);
  transition: all var(--time) ease;
}

.phone_box_container:hover .phone_txt_box {
  display: flex;
}

.phone_box {
  width: 45px;
  height: 45px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fff;
  border: 1px solid #1f2e58;
  border-radius: 50%;
  transition: all var(--time) ease;
}

.how_to_help_box a:hover .phone_box {
  background: #1f2e58;
}

.how_to_help_box a:hover .phone_box img {
  filter: invert(62%) sepia(39%) saturate(5545%) hue-rotate(336deg) brightness(113%) contrast(72%);
}

.how_to_help_box_ar a:hover .phone_box {
  background: #1f2e58;
}

.how_to_help_box_ar a:hover .phone_box img {
  filter: invert(62%) sepia(39%) saturate(5545%) hue-rotate(336deg) brightness(113%) contrast(72%);
}

/* ------------------------------------------------ */
.sixcolumn {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--gap-24);
}

/* ------------------------------------------------ */

.home_ratings_title p {
  color: #d7d7d7;
}

/* ------------------------------------------------ */

/* :global(body.rtl) .container {
    background: red;
} */

.pulse {
  animation: pulse 1s infinite ease-in-out alternate;
}

@keyframes pulse {
  from {
    transform: scale(0.8);
  }

  to {
    transform: scale(1);
  }
}

@media (max-width: 1900px) {
  .rating_sevices {
    margin-top: 145px;
  }

  .rating_sevices_ar {
    margin-top: 200px;
  }
}

@media (max-width: 1600px) {
  .rating_sevices_ar {
    margin-top: 260px;
  }

  .rating_sevices {
    margin-top: 260px;
  }
}

@media (max-width: 1500px) {
  .how_to_help_box {
    right: -7% !important;
  }
}

@media (max-width: 1400px) {
  .rating_sevices_ar {
    margin-top: 230px;
  }

  .rating_sevices {
    margin-top: 230px;
  }
}

@media (max-width: 1366px) {
  .rating_sevices {
    margin-top: 230px;
  }

  .rating_sevices_ar {
    margin-top: 230px;
  }
}

@media (max-width: 1280px) {
  .how_to_help_box {
    right: 0% !important;
  }

}

@media (max-width: 1200px) {
  .home_counter_content {
    margin-bottom: 60px;
  }

  .rating_sevices {
    margin-top: 220px;
  }

  .rating_sevices_ar {
    margin-top: 220px;
  }
}

@media (max-width: 900px) {
  .rating_sevices {
    margin-top: 220px;
  }

  .rating_sevices_ar {
    margin-top: 220px;
  }
}

@media (max-width: 860px) {
  .home_counter_content_left h2 {
    font-size: 2rem;
    line-height: 40px;
  }

  .sixcolumn {
    grid-template-columns: repeat(2, 1fr);
  }

  .rating_sevices {
    margin-top: 220px;
  }

  .rating_sevices_ar {
    margin-top: 220px;
  }
}

@media (max-width: 767px) {

  .home_counter_content_left,
  .home_counter_content_right {
    width: 100%;
  }

  .home_counter_content_left h2 {
    font-size: 1.5rem;
    line-height: 34px;
    letter-spacing: initial;
  }

  .home_counter_content {
    margin-bottom: 40px;
  }

  .rating_sevices {
    margin-top: 290px;
  }

  .rating_sevices_ar {
    margin-top: 290px;
  }
}

@media (max-width: 600px) {
  .sixcolumn {
    grid-template-columns: repeat(1, 1fr);
  }

  .home_counter::after {
    opacity: 0.3;
  }
}


/* @media (max-width: 407px) { */
@media (max-width: 500px) {
  .rating_sevices {
    margin-top: 270px;
  }

  .rating_sevices_ar {
    margin-top: 270px;
  }
}