
.about_first_wrap {
    display: flex;
    flex-flow: column wrap;
    row-gap: 230px;
}

.about_first_content {
    display: flex;
    align-items: flex-end;
    justify-content: space-between;
    row-gap: 30px;
}

.about_first_left,
.about_first_right {
    width: 46%;
}

.about_first_left span {
    display: block;
    font-size: 1.75rem;
    font-weight: 300;
    font-family: 'precioussanstwo-thin';
    margin-bottom: 5px;
}

.about_first_left h2 {
    font-size: 2.375rem;
    font-weight: 600;
}

/* ---------------------------------------------------- */

.about_last {
    /* background-image: url(../../public/images/explore_grdnt.png); */
    background-image: url(../../public/images/explore_grdnt.webp);
    background-repeat: no-repeat;
    background-position: center left 15%;
}

/* ---------------------------------------------------- */

.sixcolumn {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--gap-24);
}


/* ---------------------------------------------------- */

.about_massage {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-flow: row wrap;
    row-gap: 110px;
}

.about_massage_left {
    width: 40%;
}

.about_massage_left img {
    border-radius: var(--radius-34);
}

.about_massage_right {
    width: calc(60% - 110px);
}

.about_massage_right p {
    color: #D7D7D7;
}

.about_massage_right p+p {
    margin-top: 25px;
}

/* ---------------------------------------------------- */

.about_last_wrap {
    display: flex;
    flex-flow: column wrap;
    row-gap: 120px;
}

.about_team_list {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: var(--gap-24);
}

@media (max-width: 1200px) {
    .about_massage_left {
        width: 40%;
    }

    .about_massage_right {
        width: calc(60% - 50px);
    }

    .about_last_wrap {
        row-gap: 80px;
    }
}

@media (max-width: 1040px) {
    .about_team_list {
        grid-template-columns: repeat(3, 1fr);
    }

    .about_first_left h2 {
        font-size: 2rem;
    }

    .about_first_left span {
        font-size: 1rem;
    }

    .about_first_content {
        align-items: center;
    }
}

@media (max-width: 860px) {
    .about_team_list {
        grid-template-columns: repeat(2, 1fr);
    }

    .about_first_left h2 {
        font-size: 1.5rem;
    }

    .about_first_wrap {
        row-gap: 50px;
    }
}

@media (max-width: 767px) {
    .about_first_content {
        flex-wrap: wrap;
    }

    .about_massage {
        flex-wrap: wrap;
        row-gap: 40px;
    }

    .about_first_left,
    .about_massage_left,
    .about_massage_right,
    .about_first_right {
        width: 100%;
    }

    .sixcolumn {
        grid-template-columns: repeat(2, 1fr);
    }

    .about_last_wrap {
        row-gap: 50px;
    }

}

@media (max-width: 600px) {

    .sixcolumn,
    .about_team_list {
        grid-template-columns: repeat(1, 1fr);
    }
}