import React from "react";
import Link from "next/link";
import styles from "../request-rating/request.module.css";
import SubBanner from "@/components/innerbanner/innerbanner";
import { fetchPageBySlug, fetchServices } from "@/lib/api/PageBySlug";
import styles2 from "@/pages/find-rating/findRatings.module.css";
import Image from "next/image";
import CAPCForm from "@/components/forms/CAPCForm";
import parse from "html-react-parser";
function Index({ pageData }) {
    const banner = pageData?.acf?.banner_details || '';
    const details = pageData?.acf?.request_details || '';

    return (
        <>
            <SubBanner bannerDetails={banner} alignEnd={false} />
            <section className="py-120 bg-secondary">
                <div className="container text-white">
                    <h1 className="main_title mb-20">
                        {parse(pageData.acf.title_capc)}
                        <div className="capc_icon ">

                            <Image
                                src={pageData.acf.title_logo}
                                alt="icon"
                                width={24}
                                height={26}
                                priority
                            />
                        </div>
                    </h1>
                    <div className="mb-20 color-pera">
                        {parse(pageData.acf.description_capc)}
                    </div>
                    {details?.mail && (
                        <Link href={`mailto:${details.mail}`} className="hover-primary">
                            {details.mail}
                        </Link>
                    )}
                </div>

                <div className={styles2.first_block} style={{ margin: "50px 0 0" }}>
                    <div className="container">
                        <div
                            className={styles2.first_content}
                            data-aos="fade-up"
                            style={{ margin: "0" }}
                        >
                            <CAPCForm
                                formLabels={pageData?.acf?.form_labels}
                                servicesList={pageData.acf.capc_services}
                            />
                        </div>
                    </div>
                </div>
            </section>

        </>
    );
}

export default Index;

export async function getServerSideProps({ locale }) {
    const langCode = locale === "ar" ? "ar" : "en";
    try {
        // Reuse the request-a-rating page data for layout consistency
        const pageData = await fetchPageBySlug("apply-for-capc-certificate", langCode);
        // const serviceList = await fetchServices(langCode);

        return {
            props: {
                pageData
            },
        };
    } catch (error) {
        console.error("Failed to fetch data:", error);
        return {
            props: {
                pageData: null,
            },
        };
    }
}