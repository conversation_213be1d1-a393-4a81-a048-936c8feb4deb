import React from "react";
import Link from "next/link";
import styles from "./myaccount.module.css";
import Sidemenu from "@/components/sidemenu/sidemenu";
import Dashboard from "@/components/dashboard/dashboard";
import { fetchPageBySlug } from "@/lib/api/PageBySlug";
import ProtectedRoute from "@/components/ProtectedRoute";

const Index = () => {
	return (
		<ProtectedRoute>
			<div className={styles.my_account_right} style={{width:'100%'}}>
				<Dashboard  />
				
			</div>
		</ProtectedRoute>
		
	);
};

export default Index;

