.table_two_column tbody td:first-child {
    width: 30%;
}

.table_two_column tbody td:nth-child(2) {
    width: 57%;
}

.table_two_column tbody td:last-child {
    width: 13%;
}

/* ------------------------------------------------------------- */

.table_sorting_title {
    display: flex;
    flex-flow: row wrap;
    justify-content: flex-end;
    row-gap: 30px;
    margin-bottom: 50px;
    position: relative;
    z-index:1;
}

.table_sorting_ul {
    column-gap: 23px;
    row-gap:10px;
}

.table_sorting_ul,
.table_sorting_ul>li {
    display: flex;
    flex-flow: row wrap;
    align-items: center;
}

.table_sorting_ul>li {
    column-gap: 11px;
}

.table_sorting_ul>li label {
    color: #ffffff;
}

@media (max-width: 860px) {
    .table_sorting_title {
        margin-bottom: 30px;
    }
}

@media (max-width: 767px) {
    .table_sorting_ul>li {
        width: 100%;
    }
    .table_sorting_ul>li label {
        width:89px;
    }
}