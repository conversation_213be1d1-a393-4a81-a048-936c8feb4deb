import React, { useState, useEffect } from "react";
import Link from "next/link";
import Image from "next/image";
import invoice from "./invoices.module.css";
import Sidemenu from "@/components/sidemenu/sidemenu";
import styles from "@/pages/dashboard/myaccount.module.css";
import SelectStyle from "@/components/SelectBox/select";
import ProtectedRoute from "@/components/ProtectedRoute";
import { fetchPageBySlug, fetchRequest } from "@/lib/api/PageBySlug";
import { useRouter } from "next/router";

const Index = ({ pageData }) => {
  const [requestList, setRequestList] = useState([]); // State to store request list
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 6;
  const [userId, setUserId] = useState(null); // State to store userId

  const { locale } = useRouter();
  const langCode = locale === "ar" ? "ar" : "en";
  
  // Fetch userId from localStorage after component mounts
  useEffect(() => {
    const user = JSON.parse(localStorage.getItem("user"));
    if (user) {
      setUserId(user); // Set userId state
    }
  }, []); // Empty dependency array to run only once after the initial mount

  // Fetch request list after userId is available
  useEffect(() => {
    if (userId) {
      const fetchData = async () => {
        try {
          const data = await fetchRequest("en", "100", userId); // Example with 'en' as language code
          setRequestList(data); // Set request list state
        } catch (error) {
          console.error("Failed to fetch request data:", error);
        }
      };

      fetchData();
    }
  }, [userId]); // Re-run this effect when userId changes

  const filteredList = requestList.filter((doc) => doc.acf && doc.acf.amount);

  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = filteredList.slice(indexOfFirstItem, indexOfLastItem);

  const totalPages = Math.ceil(filteredList.length / itemsPerPage);

  const handlePageChange = (pageNumber) => {
    setCurrentPage(pageNumber);
  };

  return (
    <ProtectedRoute>
      <div className={styles.my_account_right} style={{ width: "100%" }}>
        <h4>{langCode === "ar" ? "فواتيري" : "My Invoices"}</h4>
        <div
          className={
            langCode === "en"
              ? invoice.table_style_second
              : invoice.table_style_second_ar
          }
        >
          <table>
            <thead>
              <tr>
                {pageData.acf.table_head.map((hd, index) => (
                  <th key={index}>{hd.title}</th>
                ))}
              </tr>
            </thead>
            {currentItems.length > 0 ? (
              <tbody>
                {currentItems.map((doc, index) => {
                  const date = new Date(doc.date);
                  const formatted_date = date.toLocaleDateString("en-GB", {
                    day: "2-digit",
                    month: "short",
                    year: "numeric",
                  });

                  return (
                    <tr key={index}>
                      <td>{formatted_date}</td>
                      <td>{doc.acf.amount}</td>
                      <td
                        className={
                          doc.payment_status_taxonomy
                            ? doc.payment_status_taxonomy.term_name.toLowerCase() ===
                              "paid"
                              ? "status-paid"
                              : doc.payment_status_taxonomy.term_name.toLowerCase() ===
                                "pending"
                              ? "status-pending"
                              : doc.payment_status_taxonomy.term_name.toLowerCase() ===
                                "cancelled"
                              ? "status-cancelled"
                              : ""
                            : ""
                        }
                      >
                        {doc.payment_status_taxonomy
                          ? doc.payment_status_taxonomy.term_name
                          : "Pending"}
                      </td>
                      <td>
                        {doc.payment_status_taxonomy &&
                          doc.payment_status_taxonomy.term_name.toLowerCase() ===
                            "paid" && (
                            <a
                              href={doc.acf.invoice.url}
                              className={`${
                                langCode === "en"
                                  ? invoice.pdf_link
                                  : invoice.pdf_link_ar
                              } pdf_link text-primary`}
                              target="_blank"
                              download={doc.acf.invoice.filename}
                            >
                              PDF
                              <Image
                                src="/images/dowload-primary.svg"
                                alt="Download icon"
                                width={15}
                                height={15}
                                priority
                              />
                            </a>
                          )}
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            ) : (
              <div
                style={{
                  padding: "30px 0",
                  textAlign: "center",
                  display: "block",
                  color: "white",
                }}
              >
                <p
                  style={{
                    position: "absolute",
                    width: "100%",
                    textAlign: "center",
                    bottom: "18px",
                  }}
                >
                  {langCode==='ar' ? 'لا توجد فواتير بعد' : 'Invoices Yet'}
                </p>
              </div>
            )}
          </table>
        </div>
      </div>
      {totalPages > 1 && (
        <div
          style={{
            width: "100%",
            display: "flex",
            gap: "10px 20px",
            justifyContent: "flex-end",
            paddingRight: "25px",
          }}
        >
          {Array.from({ length: totalPages }, (_, i) => i + 1).map((number) => (
            <button
              key={number}
              onClick={() => handlePageChange(number)}
              className={currentPage === number ? styles.activePage : ""}
              style={{
                background: "transparent",
                color: currentPage === number ? "white" : "#787878",
                border: "unset",
                padding: "5px 10px",
                cursor: "pointer",
              }}
            >
              {number}
            </button>
          ))}
        </div>
      )}
    </ProtectedRoute>
  );
};

export default Index;

export async function getServerSideProps({ locale }) {
  const langCode = locale === "ar" ? "ar" : "en";
  try {
    const pageData = await fetchPageBySlug("invoices", langCode);
    return {
      props: {
        pageData,
      },
    };
  } catch (error) {
    console.error("Failed to fetch page data:", error);
    return {
      props: {
        pageData: [],
      },
    };
  }
}
