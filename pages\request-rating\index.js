import React from "react";
import Link from "next/link";
import styles from "./request.module.css";
import SubBanner from "@/components/innerbanner/innerbanner";
import Threecolumn from "@/components/threecolumn/threecolumn";
import Sixcolumn from "@/components/sixcolumn/sixcolumn";
import { fetchPageBySlug, fetchServices } from "@/lib/api/PageBySlug";
import {fetchRatingTab} from "@/lib/api/byfield"
import SelectStyle from "@/components/SelectBox/select";
import styles2 from "@/pages/find-rating/findRatings.module.css";
import Image from "next/image";
import RequestForm from "@/components/forms/RequestForm";
import { useEffect } from "react";



function Index({pageData,serviceList}) {
	const banner = pageData.acf.banner_details || '';
	const details = pageData.acf.request_details || '';
	// const preparation = pageData.acf.preparation_details || '';
	// const assessment = pageData.acf.assessment || '';
	// const rating = pageData.acf.rating_dissemination || '';
 
	return (
    <>
      <SubBanner bannerDetails={banner} alignEnd={false} />
      <section className="py-120 bg-secondary">
        <div className="container text-white">
          <h1
            className="main_title mb-20"
            dangerouslySetInnerHTML={{ __html: details.title }}
          />
          <p
            className="mb-20 color-pera"
            dangerouslySetInnerHTML={{ __html: details.description }}
          />
          <Link href={`mailto:${details.mail}`} className="hover-primary">
            {details.mail}
          </Link>
        </div>

        <div className={styles2.first_block} style={{ margin: "50px 0 0" }}>
          <div className="container">
            {/* <h2 className="main_title text-center text-white mb-20" data-aos="fade-up">
							Find a Rating
						</h2> */}
            <div
              className={styles2.first_content}
              data-aos="fade-up"
              style={{ margin: "0" }}
            >
              <RequestForm
                services={serviceList}
                formLabels={pageData.acf.form_labels}
              />
            </div>
          </div>
        </div>
      </section>
      <section className="py-120 bg-light">
        <div className="container">
          <h2
            className="text-center text-secondary main_title mb-30"
            // dangerouslySetInnerHTML={{ __html: preparation.title }}
          >
            {pageData.acf.titlep}
          </h2>
          {/* <Threecolumn columns={preparation.cards} /> */}
          <div className={styles.find_rating_image_wrapper}>
            <Image src={pageData.acf.image_p} alt=""  
              width={0} 
              height={0} 
              style={{ width: "100%", height: "auto" }} loading='lazy'
              />
          </div>
        </div>
      </section>
      {/* <section className="py-120 bg-secondary">
        <div className="container"> */}
          {/* <div className="section_assessment pb-120">
						<h2 className="text-white main_title mb-30"
							dangerouslySetInnerHTML={{ __html: assessment.title }}
						/>
						<Sixcolumn
							sixColumnItems={assessment.cards}
							className={styles.assessment_columns}
						/>
					</div>
					<div className="section_dissemination pt-120">
						<h2 className="text-white main_title mb-30"
							dangerouslySetInnerHTML={{ __html: rating.title }}
						/>
						<Sixcolumn
							sixColumnItems={rating.cards}
							className={styles.dissemination_columns}
						/>
					</div> */}
        {/* </div>
      </section> */}
    </>
  );
}

export default Index;
export async function getServerSideProps({locale}) {
	const langCode = locale === "ar" ? "ar" : "en";
	try {
		const pageData = await fetchPageBySlug("request-a-rating", langCode);
		const serviceList = await fetchServices(langCode)
		return {
			props: {
				pageData,
				serviceList
			},
		};
	} catch (error) {
		console.error("Failed to fetch data:", error);
		return {
			props: {
				pageData: null
			},
		};
	}
}
