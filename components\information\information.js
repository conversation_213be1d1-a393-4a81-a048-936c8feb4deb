import React, { useState, useEffect } from "react";
import Image from "next/image";
import styles from "./information.module.css";
import { useRouter } from 'next/router';

const Information = ({ data }) => {
    const [currentPassword, setCurrentPassword] = useState("");
    const [newPassword, setNewPassword] = useState("");
    const [confirmPassword, setConfirmPassword] = useState("");
    const [error, setError] = useState(null);
    const [error1, setError1] = useState(null);
    const [successMessage, setSuccessMessage] = useState(null);
    const [userMeta, setUserMeta] = useState([]);
    // State to hold user information (non-sensitive fields like company name, etc.)
    const [userInfo, setUserInfo] = useState({
        companyName: "",
        individualName: "",
        city: "",
        companyAddress: "",
        companyIndustry: "",
        phoneNumber: "",
        emailAddress: "",
    });

    const router = useRouter();
    const { locale } = useRouter();
    const [isLoading, setIsLoading] = useState(false);
 
        const fetchUserInfo = async () => {
            const token = localStorage.getItem("token");
            if (!token) {
                setError("User is not authenticated.");
                return;
            }
    
            try {
                // Fetch user information
                const responseUserInfo = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/users/me`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    }
                });
    
                if (!responseUserInfo.ok) {
                    const errorData = await responseUserInfo.json();
                    throw new Error(errorData.message || 'Failed to fetch user data');
                }
    
                const userData = await responseUserInfo.json();
                setUserMeta(userData); // Log user data
                // Set the user data into state (for non-sensitive fields)
                
    
                // Fetch user meta information
                const responseUserMeta = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/usermeta`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                    },
                });
    
                if (!responseUserMeta.ok) {
                    const errorDataMeta = await responseUserMeta.json();
                    throw new Error(errorDataMeta.message || 'Failed to fetch user meta');
                }
    
                const userMeta = await responseUserMeta.json();
                //console.log(userMeta); // Log user meta
                // Set user meta into state if needed
                setUserMeta(userMeta);

            } catch (error) {
                setError(error.message);
                setTimeout(() => {
                    setError('');
                  }, 5000);
            }
        };
    useEffect(() => {
            // Fetch user information and user meta on component mount
        fetchUserInfo();
    }, []);
    useEffect(() => {
        setUserInfo({
            companyName: userMeta.company ? userMeta.company[0] : '',
            individualName: userMeta.first_name ? userMeta.first_name[0] : '',
            city: userMeta.city ? userMeta.city[0] : '',
            companyAddress: userMeta.company_address ? userMeta.company_address[0] : '',
            companyIndustry: userMeta.company_industry ? userMeta.company_industry[0] : '',
            phoneNumber: userMeta.phone_number ? userMeta.phone_number[0] : '',
            emailAddress: (userMeta.email_address && userMeta.email_address[0]) ? userMeta.email_address[0] : (userMeta.nickname && userMeta.nickname[0]) ? userMeta.nickname[0] : null,

        });
    }, [userMeta]);

    const handleSubmit = async (e) => {
        e.preventDefault();

        if (newPassword !== confirmPassword) {
            setError("New passwords do not match.");
            return;
        }

        const token = localStorage.getItem("token");
        if (!token) {
            setError("User is not authenticated.");
            return;
        }

        const userData = {
            old_password: currentPassword,
            new_password: newPassword,
  
          };
        try {
            // Send current and new password for verification and update
            const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/change-password`, {
                method: 'POST',
                headers: {
                  'Authorization': `Bearer ${token}`,
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify(userData),
              });

            if (!response.ok) {
                const errorData = await response.json();
                setError(errorData.error)
                throw new Error(errorData.error || 'Failed to update password');
                
            }

            // If password is updated, log out the user for security
           // 
           // Reset the form fields
           setCurrentPassword("");
           setNewPassword("");
           setConfirmPassword("");
           const data = await response.json();
           if(data.message) {
            handleLogout();
            setSuccessMessage(data.message);
            setTimeout(() => {
                setSuccessMessage("");
            }, 3000);
           } 
            


        } catch (error) {
            setError(error.message);
        }
    };

    const handleLogout = () => {
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        localStorage.removeItem('membershipType');
        router.push('/login'); // Redirect to login page
    };
   

    const handleSubmit1 = async (event) => {
        event.preventDefault();
        setIsLoading(true);
        const token = localStorage.getItem("token");
        const userId = localStorage.getItem("user");
        const userData = {
          user_id: userId,
          city: userInfo.city,
          company_address: userInfo.companyAddress,
          company_industry: userInfo.companyIndustry,
          phone_number: userInfo.phoneNumber,
          company: userInfo.companyName,
          first_name: userInfo.individualName,
          email_address: userInfo.emailAddress

        };
      
        try {
          const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/update_user_meta`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${token}`,
            },
            body: JSON.stringify(userData),
          });
      
          if (response.ok) {
            const data = await response.json();
           // console.log('User meta updated successfully:', data);
            setIsLoading(false);
            // Optionally show success alert here
          } else {
            const error = await response.json();
            console.error('Failed to update user meta:', error);
            setIsLoading(false);
            // Optionally show error alert here
          }
        } catch (error) {
          console.error('Error occurred while updating user meta:', error);
          setIsLoading(false);
          // Optionally show error alert here
        }
      };

    return (
        <>
            <div className={styles.information}>
                <div>
                    <h4>{locale === "ar" ? "معلومات الحساب" : data.acf.titles_ac.title_1}</h4>
                    
                    <form >
                        <ul className={styles.form_style}>
                            <li>
                                <input
                                    type="text"
                                    placeholder={
                                        locale === "ar" ? "اسم الشركة" : "Company Name"
                                      }
                                    value={userInfo.companyName}
                                    onChange={(e) => setUserInfo({ ...userInfo, companyName: e.target.value })}
                                />
                            </li>
                            <li>
                                <input
                                    type="text"
                                    placeholder={
                                        locale === "ar" ? "اسم الفرد" : "Individual Name"
                                      }
                                    value={userInfo.individualName}
                                    onChange={(e) => setUserInfo({ ...userInfo, individualName: e.target.value })}
                                />
                            </li>
                            <li>
                                <input
                                    type="text"
                                    placeholder={
                                        locale === "ar" ? "المدينة" : "City"
                                      }
                                    value={userInfo.city}
                                    onChange={(e) => setUserInfo({ ...userInfo, city: e.target.value })}
                                />
                            </li>
                            <li>
                                <input
                                    type="text"
                                    placeholder={
                                        locale === "ar" ? "عنوان الشركة" : "Company Address"
                                      }
                                    value={userInfo.companyAddress}
                                    onChange={(e) => setUserInfo({ ...userInfo, companyAddress: e.target.value })}
                                />
                            </li>
                            <li>
                                <input
                                    type="text"
                                    placeholder={
                                        locale === "ar" ? "صناعة الشركة" : "Company Industry"
                                      }
                                    value={userInfo.companyIndustry}
                                    onChange={(e) => setUserInfo({ ...userInfo, companyIndustry: e.target.value })}
                                />
                            </li>
                            <li>
                                <input
                                    type="text"
                                    placeholder={
                                        locale === "ar" ? "رقم الهاتف" : "Phone Number"
                                      }
                                    value={userInfo.phoneNumber}
                                    onChange={(e) => setUserInfo({ ...userInfo, phoneNumber: e.target.value })}
                                />
                            </li>
                            <li>
                                <input
                                    type="text"
                                    placeholder={
                                        locale === "ar" ? "عنوان البريد الإلكتروني" : "Email Address"
                                      }
                                    value={userInfo.emailAddress}
                                    onChange={(e) => setUserInfo({ ...userInfo, emailAddress: e.target.value })}
                                />
                            </li>
                        </ul>
                        <button
                            href="#."
                            className={`${styles.btn_style_wrap} btn_style_wrap`}
                            onClick={handleSubmit1}
                        >
                            <span className="btn_style_primary">
                            {isLoading 
                                ? (locale === "ar" ? "جار التحديث..." : "Updating...") 
                                : (locale === "ar" ? "تحديث" : "Update")}
                            </span>
                            <span className="btn_style_arrow">
                                <Image
                                    src="/images/white_right_arw.svg"
                                    alt="icon"
                                    width={22}
                                    height={15}
                                    priority
                                />
                            </span>
                        </button>
                    </form>
                    
                </div>
                <div>
                    <h4>{locale === "ar" ? "تغيير كلمة المرور" : data.acf.titles_ac.title_2}</h4>
                    <form >
                        <ul className={styles.form_style}>
                            <li>
                                <input
                                    type="password"
                                    placeholder={
                                        locale === "ar" ? "كلمة المرور الحالية" : "Current Password"
                                      }
                                    value={currentPassword}
                                    onChange={(e) => setCurrentPassword(e.target.value)}
                                    required
                                />
                            </li>
                            <li>
                                <input
                                    type="password"
                                    placeholder={
                                        locale === "ar" ? "كلمة المرور الجديدة" : "New Password"
                                      }
                                    value={newPassword}
                                    onChange={(e) => setNewPassword(e.target.value)}
                                    required
                                />
                            </li>
                            <li>
                                <input
                                    type="password"
                                    placeholder={
                                        locale === "ar" ? "تأكيد كلمة المرور" : "Confirm Password"
                                      }
                                    value={confirmPassword}
                                    onChange={(e) => setConfirmPassword(e.target.value)}
                                    required
                                />
                            </li>
                        </ul>
                        <button
                            type="submit"
                            className={`${styles.btn_style_wrap} btn_style_wrap`}
                            onClick={handleSubmit}
                        >
                            <span className="btn_style_primary">{locale === "ar" ? "تحديث" : "Update"}</span>
                            <span className="btn_style_arrow">
                                <Image
                                    src="/images/white_right_arw.svg"
                                    alt="icon"
                                    width={22}
                                    height={15}
                                    priority
                                />
                            </span>
                        </button>
                    </form>
                    {error && <p className={styles.error} style={{ color: 'red', textAlign: 'center', marginTop: '10px' }}>
                        {error}
                    </p>}
                    {successMessage && <p className={styles.success} style={{ color: 'green', textAlign: 'center', marginTop: '10px' }}>
                        {successMessage}
                    </p>}
                </div>
            </div>
        </>
    );
};

export default Information;
