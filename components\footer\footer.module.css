.footer {
  padding: var(--space-120) 0 0 0;
  position: relative;
  overflow: hidden;
  z-index: 0;
  background-image: url(../../public/images/footer_grd.png);
  background-repeat: no-repeat;
  background-position: bottom center;
}

/* .footer::after {
    content: "";
    position: absolute;
    right: 20%;
    top: 88px;
    background: #e8593b;
    border-radius: 50%;
    opacity: 0.7;
    width: 637px;
    height: 637px;
    filter: blur(367px);
    z-index: -1;
} */
p.textP span {
  font-family: var(--font-primary);
}

.footer_top {
  background: rgba(125, 125, 125, 0.4);
  display: flex;
  flex-flow: row wrap;
  align-items: flex-start;
  row-gap: 30px;
  justify-content: space-between;
  padding: 80px 40px;
  backdrop-filter: blur(25px);
  border-radius: var(--radius-34);
}

.footer_logo {
  width: 40%;
}

.footer_content {
  width: 60%;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  color: #ffffff;
}

.footer_content h4 {
  font-weight: 400;
  font-size: 0.875rem;
  text-transform: uppercase;
  opacity: 0.6;
  letter-spacing: 0.04em;
  margin-bottom: 25px;
}

:global(body.rtl) .footer_content_left {
  width: 60%;
}

.footer_content_left ul {
  display: flex;
  flex-wrap: wrap;
  row-gap: 10px;
}

.footer_content_left li {
  width: 50%;
  padding-right: 10px;
  text-transform: capitalize;
}

.footer_content_left a:hover {
  color: var(--color-primary);
}

.footer_content_right {
  text-align: right;
}

.footer_content_right>* {
  display: block;
}

.footer_phone:hover,
.footer_email:hover {
  color: var(--color-primary);
}

.footer_email {
  margin-top: 12px;
  font-family: var(--font-primary);
  ;
}

.footer_content_right p {
  margin-top: 20px;
}

/* ----------------------------------------------------- */

.footer_bottom {
  margin-top: 48px;
  width: 100%;
}

.newsletter {
  display: flex;
  justify-content: space-between;
}

.newsletter h3 {
  font-size: 2.5rem;
  color: #ffffff;
  width: 40%;
}

:global(body.rtl) .newsletter h3 {
  /* font-size: 2rem; */
  max-width: 400px;
}

@media (max-width: 1366px) {
  :global(body.rtl) .newsletter h3 {
    font-size: 2rem;
  }
}

.newsletter_content p {
  font-size: 0.875rem;
  color: #ffffff;
  margin-bottom: 8px;
}

.newsletter_right {
  display: flex;
  align-items: center;
  width: 60%;
  justify-content: space-between;
}

.newsletter_form {
  display: flex;
  flex-flow: row wrap;
  align-items: center;
  width: 438px;
  border-radius: 12px;
  border: 1px solid #46536e;
}

.newsletter_form>* {
  height: 40px;
  background: transparent;
  border: none;
}

.newsletter_form input {
  padding: 0 16px;
  color: #ffffff;
  width: calc(100% - 70px);
  text-transform: uppercase;
  font-size: 13px;
}


.newsletter_form input:focus+button img {
  filter: invert(62%) sepia(39%) saturate(5545%) hue-rotate(336deg) brightness(113%) contrast(72%);
}

.newsletter_form button {
  width: 70px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.footer_copyright {
  color: #ffffff;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25px 0;
  margin-top: 20px;
  border-top: 1px solid #46536e;
}

.footer_copyright p {
  font-size: 0.875rem;
  margin: 0;
}

.privacy_terms {
  --space: 35px;
  display: flex;
  flex-flow: row wrap;
}

.privacy_terms a:hover {
  color: var(--color-primary);
}

.privacy_terms li:not(:last-child) {
  padding-right: var(--space);
  margin-right: var(--space);
  border-right: 1px solid #ffffff;
}

.privacy_terms li a {
  text-transform: capitalize;
}

.privacy_terms_ar {
  --space: 35px;
  display: flex;
  flex-flow: row wrap;
}

.privacy_terms_ar a:hover {
  color: var(--color-primary);
}

.privacy_terms_ar li a {
  text-transform: capitalize;
}

.privacy_terms_ar li:not(:last-child) {
  padding-left: var(--space);
  margin-left: var(--space);
  border-left: 1px solid #ffffff;
}

@media (max-width: 1600px) {
  .footer_logo {
    width: 35%;
  }

  .footer_content {
    width: 65%;
  }
}

@media (max-width: 1200px) {
  .footer_logo {
    width: 22%;
  }

  .footer_content {
    width: 68%;
  }

  .newsletter h3 {
    font-size: 2rem;
    width: 25%;
  }

  :global(body.rtl) .newsletter h3 {
    font-size: 1.8rem;
    max-width: unset;

  }

  .newsletter_right {
    width: 70%;
  }

  .privacy_terms {
    --space: 25px;
  }

  .privacy_terms_ar {
    --space: 25px;
  }
}

@media (max-width: 1040px) {
  .newsletter_form {
    width: 340px;
  }

  .newsletter h3 {
    font-size: 1.5rem;
  }

  :global(body.rtl) .newsletter h3 {
    font-size: 1.3rem;
  }
}

@media (max-width: 920px) {
  :global(body.rtl) .footer_content_left {
    width: 50%;
  }
}

@media (max-width: 860px) {
  .footer_logo {
    width: 100%;
  }

  .footer_content {
    width: 100%;
  }

  .footer_top {
    padding: 50px 30px;
  }

  :global(body.rtl) .footer_content_left {
    width: 65%;
  }
}

@media (max-width: 767px) {
  .newsletter h3 {
    width: 100%;
  }

  .newsletter {
    flex-wrap: wrap;
    row-gap: 20px;
    text-align: center;
  }

  .newsletter_form input {
    font-size: 16px;
  }

  .newsletter_right {
    flex-wrap: wrap;
    row-gap: 15px;
    flex-direction: column;
  }

  .newsletter_form,
  .newsletter_right {
    width: 100%;
  }

  .newsletter_content {
    width: 100%;
    flex-wrap: wrap;
    flex-direction: column-reverse;
  }

  .privacy_terms {
    --space: 15px;
  }

  .privacy_terms_ar {
    --space: 15px;
  }

  .footer_copyright {
    flex-direction: column-reverse;
    row-gap: 15px;
  }

  .footer_content_left ul {
    column-gap: 15px;
  }

  .footer_content_left li {
    padding: 0;
    width: auto;
  }

  .footer_content {
    row-gap: 25px;
  }

  .footer_top {
    padding: 30px 20px;
  }

  .footer_content h4 {
    font-size: 0.875rem;
    margin-bottom: 15px;
    opacity: 1;
  }

  .footer_content_right {
    text-align: left;
  }

  :global(body.rtl) .footer_content_right {
    text-align: right;
  }

  .newsletter h3 br {
    display: none;
  }

  .footer_content_right p {
    margin-top: 15px;
  }

  :global(body.rtl) .footer_content_left {
    width: 50%;
  }
}

@media (max-width: 540px) {
  :global(body.rtl) .footer_content_left {
    width: 100%;
  }
}