import { useState, useEffect } from "react";
import Image from "next/image";
import Link from "next/link";
import styles from "./contact.module.css";
import { fetchPageBySlug } from "@/lib/api/PageBySlug";
import FormContact from "@/components/forms/FormContact";
import Head from "next/head";
import { useRouter } from "next/router";
import { GoogleMap, Marker, useLoadScript, InfoWindow } from '@react-google-maps/api';

const mapContainerStyle = {
  width: '100vw',
  height: '100%',
};



const mapStyles = [
  
    {
        "featureType": "all",
        "elementType": "labels.text.fill",
        "stylers": [
            {
                "saturation": 36
            },
            {
                "color": "#000000"
            },
            {
                "lightness": 40
            }
        ]
    },
    {
        "featureType": "all",
        "elementType": "labels.text.stroke",
        "stylers": [
            {
                "visibility": "on"
            },
            {
                "color": "#000000"
            },
            {
                "lightness": 16
            }
        ]
    },
    {
        "featureType": "all",
        "elementType": "labels.icon",
        "stylers": [
            {
                "visibility": "off"
            }
        ]
    },
    {
        "featureType": "administrative",
        "elementType": "geometry.fill",
        "stylers": [
            {
                "color": "#000000"
            },
            {
                "lightness": 20
            }
        ]
    },
    {
        "featureType": "administrative",
        "elementType": "geometry.stroke",
        "stylers": [
            {
                "color": "#000000"
            },
            {
                "lightness": 17
            },
            {
                "weight": 1.2
            }
        ]
    },
    {
        "featureType": "landscape",
        "elementType": "geometry",
        "stylers": [
            {
                "color": "#000000"
            },
            {
                "lightness": 20
            }
        ]
    },
    {
        "featureType": "poi",
        "elementType": "geometry",
        "stylers": [
            {
                "color": "#000000"
            },
            {
                "lightness": 21
            }
        ]
    },
    {
        "featureType": "road.highway",
        "elementType": "geometry.fill",
        "stylers": [
            {
                "color": "#000000"
            },
            {
                "lightness": 17
            }
        ]
    },
    {
        "featureType": "road.highway",
        "elementType": "geometry.stroke",
        "stylers": [
            {
                "color": "#000000"
            },
            {
                "lightness": 29
            },
            {
                "weight": 0.2
            }
        ]
    },
    {
        "featureType": "road.arterial",
        "elementType": "geometry",
        "stylers": [
            {
                "color": "#000000"
            },
            {
                "lightness": 18
            }
        ]
    },
    {
        "featureType": "road.local",
        "elementType": "geometry",
        "stylers": [
            {
                "color": "#000000"
            },
            {
                "lightness": 16
            }
        ]
    },
    {
        "featureType": "transit",
        "elementType": "geometry",
        "stylers": [
            {
                "color": "#000000"
            },
            {
                "lightness": 19
            }
        ]
    },
    {
        "featureType": "water",
        "elementType": "geometry",
        "stylers": [
            {
                "color": "#000000"
            },
            {
                "lightness": 17
            }
        ]
    }

];

function Contact({ pageData }) {

    const center = {
        lat: parseFloat(pageData.acf.contact_details.lat), 
        lng: parseFloat(pageData.acf.contact_details.long),
      };
      const location = {
          content: 'Tassneif',
          lat: parseFloat(pageData.acf.contact_details.lat), 
          lng: parseFloat(pageData.acf.contact_details.long),
          icon: 'images/loc.png',
        };

  const { isLoaded, loadError } = useLoadScript({
    googleMapsApiKey: 'AIzaSyCaWcgwHmig2q4SmrOXzGuPmf1PP5g-iao&', // Replace with your actual API key
  });

  const [selectedMarker, setSelectedMarker] = useState(null);
  const [showInfoWindow, setShowInfoWindow] = useState(false);
  const { locale } = useRouter();
  const langCode = locale === 'ar' ? 'ar' : 'en';

  useEffect(() => {
    if ("scrollRestoration" in window.history) {
      window.history.scrollRestoration = "manual"; // Disable automatic restoration
    }
  }, []);

  if (loadError) return <div></div>;
  if (!isLoaded) return <div></div>;

  return (
    <div>
      <section
        className={styles.contact_banner}
        style={{
          //backgroundImage: `url(${pageData.acf.banner_details.image})`,
          backgroundSize: "cover",
          backgroundPosition: "center",
          backgroundRepeat: "no-repeat",
        }}
      >
        <GoogleMap
          mapContainerStyle={mapContainerStyle}
          zoom={11}
          center={center}
          options={{ styles: mapStyles,
            zoomControl: true,
            scrollwheel: false,
            disableDefaultUI: true,

           }}
          
        >
         <Marker
            position={{ lat: location.lat, lng: location.lng }}
            icon={{
                url: location.icon,
                scaledSize: new window.google.maps.Size(25, 30), 
              }}
            animation={google.maps.Animation.DROP}
            onClick={() => setShowInfoWindow(true)}
          />
          {/* {showInfoWindow && (
            <InfoWindow
              position={{ lat: location.lat, lng: location.lng }}
              onCloseClick={() => setShowInfoWindow(false)}
            >
              <div>{location.content}</div>
            </InfoWindow>
          )} */}
        </GoogleMap>
      </section>


      <div className="bg_main">
        <section
          className={`${styles.contact_top_section} pt-100  bg-secondary`}
        >
          <div className="container relative">
            <div className={styles.contact_form_main}>
              <div className="text-center text-white" data-aos="fade-up">
                <h2
                  className="main_title"
                  dangerouslySetInnerHTML={{
                    __html: pageData.acf.contact_details.form_title,
                  }} 
                />
                <p
                  dangerouslySetInnerHTML={{
                    __html: pageData.acf.contact_details.form_description,
                  }}
                />
              </div>

              <div className={styles.form_wrap} data-aos="fade-up">
                <FormContact />
              </div>

              <div
                className={`${langCode=== "ar" ? styles.contact_details_ar : styles.contact_details} pt-100`}
                data-aos="fade-up"
              >
                <ul>
                  <li>
                    <div className={styles.contact_icon}>
                      <Image
                        alt="icon"
                        width="32"
                        height="24"
                        src="/images/mail.png" loading='lazy'
                      />
                    </div>
                    <h4>
                      {pageData.acf.contact_details.mail && (
                        <Link
                          href={`mailto:${pageData.acf.contact_details.mail}`}
                        >
                          {pageData.acf.contact_details.mail}
                        </Link>
                      )}
                    </h4>
                  </li>

                  <li>
                    <div className={styles.contact_icon}>
                      <Image
                        alt="icon"
                        width="27"
                        height="26"
                        src="/images/call.png" loading='lazy'
                      />
                    </div>
                    <h4>
                      {pageData.acf.contact_details.phone && (
                        <Link
                          href={`tel:${pageData.acf.contact_details.phone}`}
                        >
                          {pageData.acf.contact_details.phone}
                        </Link>
                      )}
                    </h4>
                  </li>

                  <li>
                    <div className={styles.contact_icon}>
                      <Image
                        alt="icon"
                        width="24"
                        height="30"
                        src="/images/map.png" loading='lazy'
                      />
                    </div>
                    <p
                      dangerouslySetInnerHTML={{
                        __html: pageData.acf.contact_details.address,
                      }}
                    />
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </section>
      </div>
    </div>
  );
}

export default Contact;

export async function getStaticProps({ locale }) {
	const langCode = locale === "ar" ? "ar" : "en";
	try {
		const pageData = await fetchPageBySlug("contact", langCode);

		return {
			props: {
				pageData,
			},
			revalidate: 10, 
		};
	} catch (error) {
		console.error("Failed to fetch data:", error);
		return {
			props: {
				pageData: null,
			},
		};
	}
}
