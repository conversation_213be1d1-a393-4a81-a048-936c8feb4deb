.container{
    overflow: hidden;
    text-align: center;
}

.longTerm {
    display: flex;
    flex-flow: column wrap;
    row-gap: 20px;
    max-width: 520px;
    padding: 6px 0;
}
.longTerm h2{
    color:#ffffff;
    text-align: center;
    margin-bottom:20px;
}

.longTerm_item {
    display: flex;
    justify-content: space-between;
}

.rating_right_multi {
    width: 45%;
    display: flex;
    flex-flow: column wrap;
    justify-content: space-between;
}

.rating_right_multi .rating_right {
    width: 100%;
}

.rating_left,
.rating_right {
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 0.5rem;
    row-gap: 3px;
    width: 45%;
    color: var(--color);
    border: 2px dashed var(--color);
    font-size: 0.9rem;
    margin-top: 5px;
    position: relative;
}

.rating_left {
    border-right: 0;
    text-align: left;
    border-radius: 12px 0 0 12px;
}

.rating_right {
    border-left: 0;
    text-align: right;
    min-height: 90px;
    border-radius: 0 12px 12px 0;
}

.rating_center {
    width: 10%;
    color: #ffffff;
    text-align: center;
}

.rating_center ul {
    display: flex;
    flex-flow: column wrap;
    row-gap: 18px;
}

.rating_center li {
    position: relative;
}

.rating_center .shortRating {
    position: absolute;
    left:20vw;
    top:0;
    background-color: #ffffff;
    color: #1a1a2e;
    padding:2px;
    width:40px;
    min-height:40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 5px;
    text-align: center;
    font-weight: bold;
}

.rating_center li::after {
    content: "";
    position: absolute;
    left: 50px;
    top: 50%;
    width: 100vw;
    border: 1px dashed #ffffff;
    opacity: 0.11;
}

.rating_right::after,
.rating_right::before,
.rating_left::after,
.rating_left::before {
    content: "";
    position: absolute;
    transform: translateY(-50%);
    border: 8px solid transparent;
}

.rating_left::after {
    right: -10px;
    top: -2px;
    border-left-color: var(--color);
}

.rating_left::before {
    right: -10px;
    bottom: -16px;
    border-left-color: var(--color);
}

.rating_right::after {
    left: -10px;
    top: -2px;
    border-right-color: var(--color);
}

.rating_right::before {
    left: -10px;
    bottom: -16px;
    border-right-color: var(--color);
}

.rating_img_wrapper{
    height: 700px;
}

.rating_img_wrapper>img{
    height: 100%;
}
