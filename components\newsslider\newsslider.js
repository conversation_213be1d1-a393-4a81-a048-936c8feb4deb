import React, { useEffect, useState } from "react";
import styles from "./newsslider.module.css";
import { Navigation } from "swiper/modules";
import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/css";
import Newscard from "../newscard/newscard";
import { fetchNews } from "@/lib/api/PageBySlug";
import { useRouter } from "next/router";

const Newsslider = ({ text }) => {
  const [options, setOptions] = useState([]);
  const { locale } = useRouter();

  useEffect(() => {
    const fetchOptions = async () => {
      try {
        const newsList = await fetchNews(locale, "10");
        setOptions(newsList);
      } catch (error) {
        console.error("Failed to fetch data:", error);
      }
    };

    fetchOptions();
  }, [locale]);

  return (
    <>
      {options.length > 0 ? (
        <Swiper
          navigation={{
            nextEl: "swiper-button-next",
            prevEl: "swiper-button-prev",
          }}
          modules={[Navigation]}
          loop={true}
          slidesPerView={"auto"}
          breakpoints={{
            0: {
              slidesPerView: 1.5,
              spaceBetween: 10,
            },
            640: {
              slidesPerView: 2,
              spaceBetween: 20,
            },
            768: {
              slidesPerView: 3,
              spaceBetween: 20,
            },
            1201: {
              slidesPerView: 4,
              spaceBetween: 40,
            },
          }}
        >
          {options.map((item) => (
            <SwiperSlide key={item.id}>
              <div className={styles.newsslider}>
                <Newscard item={item} />
              </div>
            </SwiperSlide>
          ))}
        </Swiper>
      ) : (
        <h3
          style={{ textAlign: "center", color: "#a7a7a7", fontWeight: "300" }}
          dangerouslySetInnerHTML={{
            __html: text,
          }}
        />
      )}
      {options.length > 3 ? (
        <>
          <div className="swiper-button-next"></div>
          <div className="swiper-button-prev"></div>
        </>
      ) : (
        ""
      )}
    </>
  );
};

export default Newsslider;
