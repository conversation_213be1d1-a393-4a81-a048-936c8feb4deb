.counter_list {
  display: flex;
  color: #ffffff;
  /* gap: 50px; */
  justify-content: space-between;
  text-align: center;
}

.counter_list_ar {
  display: flex;
  color: #ffffff;
  /* gap: 50px; */
  justify-content: space-between;
  text-align: center;
}

.counter_item {
  min-width: 155px;
}

.counter_item_ar {
  min-width: 155px;
}

.counter {
  text-align: left;
}

.counter_ar {
  text-align: right;
}

.counter_item p:last-child {
  padding-top: 22px;
  margin: 22px 0 0 0;
  color: #d7d7d7;
  border-top: 1px solid #c5d2e7;
  text-align: left;
}

.counter_item_ar p:last-child {
  padding-top: 22px;
  margin: 22px 0 0 0;
  color: #d7d7d7;
  border-top: 1px solid #c5d2e7;
  text-align: right;
}

.counter_item span {
  font-size: 3.4375rem;
  line-height: 66px;
}

/* .counter_item span:not(:nth-child(2)) {
  color: var(--color-primary);
} */
.counter_item .special_char {
  color: var(--color-primary);
}

.counter_item_ar span {
  font-size: 3.4375rem;
  line-height: 66px;
}

/* .counter_item_ar span:not(:nth-child(1)) {
  color: var(--color-primary);
} */
.counter_item_ar .special_char {
  color: var(--color-primary);
}

@media (max-width: 1440px) {
  .counter_item span {
    font-size: 2.4375rem;
    line-height: 50px;
  }

  .counter_item_ar span {
    font-size: 2.4375rem;
    line-height: 50px;
  }
}

@media (max-width: 1200px) {
  .counter {
    text-align: start;
  }

  .counter_item span {
    font-size: 2.4rem;
    line-height: 50px;
  }

  .counter_item p:last-child {
    padding-top: 15px;
    margin: 15px 0 0 0;
  }

  .counter_ar {
    text-align: start;
  }

  .counter_item_ar span {
    font-size: 2.3rem;
    line-height: 50px;
  }

  .counter_item_ar p:last-child {
    padding-top: 15px;
    margin: 15px 0 0 0;
  }
}

@media (max-width: 1030px) {
  .counter_list {
    flex-wrap: wrap;
    gap: 30px;
    justify-content: flex-start;
  }

  .counter_list_ar {
    flex-wrap: wrap;
    gap: 30px;
  }

  .counter_item {
    width: calc(25% - 30px);
  }

  .counter_item_ar {
    width: calc(25% - 30px);
  }

  .counter {
    text-align: start;
  }
}

@media (max-width: 860px) {
  .counter_list {
    flex-wrap: wrap;
    gap: 20px;
    row-gap: 30px;
  }

  .counter_list_ar {
    flex-wrap: wrap;
    gap: 20px;
    row-gap: 30px;

  }

  .counter_item {
    width: calc(33.33% - 30px);
  }

  .counter_item span {
    font-size: 2rem;
    line-height: 50px;
  }

  .counter_item p:last-child {
    padding-top: 10px;
    margin: 10px 0 0 0;
  }

  .counter_item_ar {
    width: calc(33.33% - 30px);
  }

  .counter_item_ar span {
    font-size: 2rem;
    line-height: 50px;
  }

  .counter_item_ar p:last-child {
    padding-top: 10px;
    margin: 10px 0 0 0;
  }
}

@media (max-width: 767px) {
  .counter_item {
    width: calc(50% - 30px);
  }

  .counter_item_ar {
    width: calc(50% - 30px);
  }
}

@media (max-width: 480px) {
  .counter {
    text-align: center;
  }

  .counter_ar {
    text-align: center;
  }

  .counter_item {
    /* width: 100%; */
    width: calc(50% - 10px);
  }

  .counter_item_ar {
    /* width: 100%; */
    width: calc(50% - 10px);
  }

  .counter_item p:last-child {
    text-align: center;
  }

  .counter_item_ar p:last-child {
    text-align: center;
  }
}