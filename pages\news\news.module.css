.news_top_section {
    width: 100%;
}

.news_top_section .container {
    row-gap: 30px;
}

.news_top_left {
    width: 55%;
    position: relative;
    overflow: hidden;
    -webkit-border-radius: 11px;
    -moz-border-radius: 11px;
    border-radius: 11px;
}

.news_top_left img {
    width: 100%;
    height: 558px;
    object-fit: cover;
    -webkit-transition: all 0.3s ease-in-out;
    -moz-transition: all 0.3s ease-in-out;
    transition: all 0.3s ease-in-out;
}

.news_top_left span {
    width: 72px;
    line-height: 26px;
    font-size: 14px;
    color: #09005D;
    text-align: center;
    -webkit-border-radius: 9px;
    -moz-border-radius: 9px;
    border-radius: 9px;
    position: absolute;
    z-index: 9;
    top: 25px;
    left: 25px;
    background: rgba(255, 255, 255, 0.7);
}

.news_top_left:hover img {
    -webkit-transform: scale(1.1, 1.1);
    -moz-transform: scale(1.1, 1.1);
    transform: scale(1.1, 1.1);
}

.news_main_title {
    width: 100%;
    padding: 25px 150px 25px 25px;
    position: absolute;
    bottom: 0;
    left: 0;
}

.news_main_title h4 {
    color: #fff;
    font-family: 'precioussanstwo-bold';
    font-size: 1.1875rem;
    line-height: 29px;
    margin-top: 15px;
}

:global(body.rtl) .news_main_title h4 {
    font-family: var(--font-arabic);
    font-weight: 500;
}

.news_main_title label {
    font-size: 0.9375rem;
    line-height: 22px;
    color: #E8593B;
    position: relative;
    font-family: 'precioussanstwo-medium';
    font-weight: 500;
    padding-left: 34px;
    position: relative;
}

:global(body.rtl) .news_main_title label {
    font-family: var(--font-arabic);
    
}


.news_main_title label::after {
    content: "";
    width: 25px;
    height: 2px;
    background-color: #E8593B;
    position: absolute;
    left: 0;
    top: 9px;
}

.news_top_right {
    width: 45%;
    padding-left: 20px
}

.news_top_right ul {
    display: flex;
    flex-wrap: wrap;
    row-gap: 20px;
}

.news_top_right ul li {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    align-items: flex-end;
}

.news_left_img {
    width: 212px;
    height: auto;
    -webkit-border-radius: 11px;
    -moz-border-radius: 11px;
    border-radius: 11px;
    position: relative;
    overflow: hidden;
}

.news_left_img img {
    width: 100%;
    -webkit-transition: all 0.3s ease-in-out;
    -moz-transition: all 0.3s ease-in-out;
    transition: all 0.3s ease-in-out;
}

.news_left_img:hover img {
    -webkit-transform: scale(1.1, 1.1);
    -moz-transform: scale(1.1, 1.1);
    transform: scale(1.1, 1.1);
}

.news_left_content {
    width: calc(100% - 212px);
    padding-left: 15px;
}

.news_left_content h4 {
    color: #fff;
    font-family: 'precioussanstwo-bold';
    font-size: 1.125rem;
    line-height: 29px;
    margin-top: 10px;
}

:global(body.rtl) .news_left_content h4{
    font-family: var(--font-arabic);
    font-weight: 500;
}

.news_left_content label {
    font-size: 0.9375rem;
    line-height: 22px;
    color: #E8593B;
    position: relative;
    font-family: 'precioussanstwo-medium';
    font-weight: 500;
    padding-left: 34px;
    position: relative;
}

:global(body.rtl) .news_left_content label{
    font-family: var(--font-arabic);
}

.news_left_content label::after {
    content: "";
    width: 25px;
    height: 2px;
    background-color: #E8593B;
    position: absolute;
    left: 0;
    top: 9px;
}

.news_list_section {
    width: 100%;
}


/* ------------------------------------------------------- */

.news_list {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    column-gap: 12px;
    row-gap: 40px;
}

:global(body.rtl) .news_top_left span {
    width: 100px;
}

@media (max-width: 1200px) {
    .news_top_right ul li {
        align-items: center;
    }
}
@media (max-width: 860px) {

    .news_top_left,
    .news_top_right {
        width: 100%;
    }

    .news_top_right {
        padding: 0;
    }
    .news_main_title {
        padding: 25px;
    }
}

@media (max-width: 767px) {
    .news_list {
        grid-template-columns: repeat(2, 1fr);
        row-gap: 20px;
    }

    .news_left_img {
        width: 120px;
    }

    .news_left_content {
        width: calc(100% - 120px);
    }

    .news_left_content h4 {
        line-height: 24px;
        font-size: 1rem;
    }

    .news_top_right ul li {
        align-items: flex-start;
    }

    .news_left_content label::after {
        width: 20px;
    }

    .news_left_content label {
        padding-left: 28px;
    }
}

@media (max-width: 600px) {
    .news_list {
        grid-template-columns: repeat(1, 1fr);
    }
}