"use client";
import React from "react";
import { useState, useEffect } from "react";
import styles from "./header.module.css";
import Link from "next/link";
import Image from "next/image";
import { AllOptions } from "@/lib/api/Options";
import { useRouter } from "next/router";

import AOS from "aos";
import "aos/dist/aos.css";

const Header = (props) => {
  const { locale } = useRouter();
  const langCode = locale === "ar" ? "ar" : "en";
  const [lc, setLc] = useState(locale);
  const [options, setOptions] = useState(null);
  const { asPath } = useRouter();
  const switchLocale = locale === "en" ? "ar" : "en";
  const router = useRouter();

  useEffect(() => {
    const fetchOptions = async () => {
      try {
        const res = await fetch(
          `${process.env.NEXT_PUBLIC_API_BASE_URL}/options/all?lang=${langCode}`
        );
        const data = await res.json();
        setOptions(data);
      } catch (error) {
        console.error("Error fetching options:", error);
      }
    };

    fetchOptions();
  }, [locale]);

  const [isOpen, setIsOpen] = useState(false);

  const toggleMenu = () => {
    setIsOpen((prev) => !prev);
    setExpandedMenuIndex(null);
  };
  useEffect(() => {
    if (isOpen) {
      document.body.classList.add("nav-open");
    } else {
      document.body.classList.remove("nav-open");
    }
    return () => {
      document.body.classList.remove("nav-open");
    };
  }, [isOpen]);

  const [isSticky, setIsSticky] = useState(false);
  const [expandedMenuIndex, setExpandedMenuIndex] = useState(null);

  useEffect(() => {
    AOS.init({
      easing: "ease",
      duration: 1000,
      once: false,
      offset: 50,
    });
    AOS.refresh();
    return () => { };
  }, []);

  useEffect(() => {
    const handleRouteChange = () => {
      if (isOpen) {
        setIsOpen(false);
      }
    };

    router.events.on("routeChangeStart", handleRouteChange);

    // Cleanup listener
    return () => {
      router.events.off("routeChangeStart", handleRouteChange);
    };
  }, [isOpen, router.events]);

  useEffect(() => {
    const handleScroll = () => {
      const offset = window.scrollY;
      const header = document.getElementById("header");

      if (header) {
        // Check if the header element exists
        if (offset > 0) {
          setIsSticky(true);
          header.classList.add(styles.sticky);
        } else {
          setIsSticky(false);
          header.classList.remove(styles.sticky);
        }
      }
    };

    window.addEventListener("scroll", handleScroll);
    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);

  const handleArrowClick = (index) => {
    setExpandedMenuIndex(expandedMenuIndex === index ? null : index);
  };

  if (!options) return null;
  //console.log('options.menu_header.main_menu', options.menu_header.main_menu);


  return (
    <>
      <header
        id="header"
        className={`${styles.header} ${isSticky ? styles.sticky : ""}`}
      >
        <div className="container">
          <div className={styles.top_header}>
            <ul
              className={
                langCode === "en"
                  ? styles.top_header_ul
                  : styles.top_header_ul_ar
              }
            >
              <li>
                <Link
                  href={options.menu_header.capc_button.url}
                  className={`${styles.request_rating} ${styles.header_top_btn}`}
                >
                  <Image
                    src={options.menu_header.capc_icon}
                    alt="icon"
                    width={24}
                    height={26}
                    priority
                  />
                  {options.menu_header.capc_button.title}
                </Link>
              </li>
              <li>
                <Link href="/dashboard">
                  <Image
                    src="/images/usr-star.png"
                    alt="user"
                    width={15}
                    height={15}
                    priority
                  />
                  {options.menu_header.dashboard.title}
                </Link>
              </li>
              <li>
                <Link
                  href={asPath}
                  locale={switchLocale}
                  // onClick={() => window.location.replace(`/${switchLocale}${asPath}`)}
                  className={`${styles.language_btn} language_btn`}
                >
                  {locale === "en" ? "عربي" : "EN"}
                </Link>
              </li>
            </ul>
          </div>
          <div className={styles.main_header}>
            <div className={locale === "en" ? styles.logo : styles.logo_ar}>
              <Link href="/">
                <Image
                  src={options.logo}
                  alt="logo"
                  width={126}
                  height={61}
                  priority
                />
              </Link>
            </div>
            <div className={styles.header_nav}>
              <nav className={`${styles.navbar} ${isOpen ? styles.slow : ""}`}>
                <ul className={styles.menu_ul}>
                  {options.menu_header.main_menu.map((menuItem, index) => (
                    <li
                      key={index}
                      className={`${expandedMenuIndex === index ? `${styles.active} changeMenu` : ""
                        } ${styles.menu_drop_li}`}

                    >
                      <div className={styles.menu_li_hover_block}>
                        <Link
                          href={menuItem.item.url}
                          dangerouslySetInnerHTML={{
                            __html: menuItem.item.title,
                          }}
                        />
                        {menuItem.have_submenu_ && (
                          <span className={`${styles.submenu_arw} submenu_arw`}>
                            <Image
                              src="/images/down_wt_arw.svg"
                              alt="logo"
                              width={10}
                              height={10}
                              priority
                              onClick={() => handleArrowClick(index)}
                            />
                          </span>
                        )}
                      </div>
                      {menuItem.have_submenu_ && (
                        <div
                          className={
                            menuItem.sub_menu
                              ? styles.mega_menu
                              : styles.sub_menu
                          }
                        >
                          <ul className={styles.sub_menu_inner}>
                            {
                              menuItem.sub_menu && (
                                <div className={styles.mega_menu_grid}>
                                  {menuItem.sub_menu && (
                                    <li
                                      className={
                                        langCode === "ar"
                                          ? styles.mega_menu_column_ar
                                          : styles.mega_menu_column
                                      }
                                    >
                                      <div
                                        className={styles.mega_menu_title_desc}
                                      >
                                        <h4
                                          dangerouslySetInnerHTML={{
                                            __html: menuItem.sub_menu.text,
                                          }}
                                        />
                                        <p
                                          dangerouslySetInnerHTML={{
                                            __html:
                                              menuItem.sub_menu
                                                .short_description,
                                          }}
                                        />
                                      </div>
                                      <ul>
                                        {menuItem.sub_menu.sub_items.map(
                                          (subItem, innerIndex) => (
                                            <li key={innerIndex}>
                                              <Link
                                                onClick={() =>
                                                  handleArrowClick(index)
                                                }
                                                href={subItem.sub_menu_item.url}
                                                dangerouslySetInnerHTML={{
                                                  __html:
                                                    subItem.sub_menu_item.title,
                                                }}
                                              />
                                            </li>
                                          )
                                        )}
                                      </ul>
                                    </li>
                                  )}
                                </div>
                              )
                              // : (
                              // 	// Regular sub-menu
                              // 	menuItem.subMenuItems.map(
                              // 		(subMenuItem, subIndex) => (
                              // 			<li key={subIndex}>
                              // 				<Link href={subMenuItem.url}>
                              // 					{subMenuItem.label}
                              // 				</Link>
                              // 			</li>
                              // 		)
                              // 	)
                              // )
                            }
                          </ul>
                        </div>
                      )}
                    </li>
                  ))}
                </ul>
                <ul className={`${styles.extra_menu} ${styles.mobile_menu}`}>
                  <li>
                    <Link
                      href={options.menu_header.button_1.url}
                      className={styles.request_rating}
                    >
                      <Image
                        src="/images/request_star.svg"
                        alt="icon"
                        width={24}
                        height={26}
                        priority
                      />
                      {options.menu_header.button_1.title}
                    </Link>
                  </li>
                  <li>
                    <Link
                      href={options.menu_header.button_2.url}
                      className={styles.find_rating}
                    >
                      {options.menu_header.button_2.title}
                    </Link>
                  </li>
                </ul>
              </nav>
              <ul className={styles.extra_menu}>
                <li>
                  <Link
                    href={options.menu_header.button_1.url}
                    className={styles.request_rating}
                  >
                    <Image
                      src="/images/request_star.svg"
                      alt="icon"
                      width={24}
                      height={26}
                      priority
                    />
                    {options.menu_header.button_1.title}
                  </Link>
                </li>
                <li>
                  <Link
                    href={options.menu_header.button_2.url}
                    className={styles.find_rating}
                  >
                    {options.menu_header.button_2.title}
                  </Link>
                </li>
              </ul>
            </div>
            <div
              className={`${langCode === "en" ? styles.menu_icon : styles.menu_icon_ar
                } ${isOpen ? styles.open : ""}`}
              onClick={toggleMenu}
            >
              <span></span>
              <span></span>
              <span></span>
              <span></span>
            </div>
          </div>
        </div>
      </header>
    </>
  );
};

export default Header;
