import Image from "next/image";
import Link from "next/link";
import styles from "./newsdetail.module.css";
import Newscard from "@/components/newscard/newscard";
import { fetchByPost, fetchRelatedNews } from "@/lib/api/PageBySlug";
import Router, { useRouter } from "next/router";
import { useEffect, useState } from "react";

//  const formatDate = (dateString, locale = "en") => {
//    const date = new Date(dateString);
//    const day = date.getDate();
//    const month = date.toLocaleString(locale, { month: "long" });
//    const year = date.getFullYear();

//    return `${day}, ${month}, ${year}`;
//  };

 const formatDate = (dateString, locale = "en") => {
   const date = new Date(dateString);
   const day = date.getDate();
   const month = date.toLocaleString(locale, { month: "long" });
   const year = date.getFullYear();

   return `${day}, ${month}, ${year}`;
 };

function Newsdetails({ pageData, related }) {
  const { locale } = useRouter();
  const langCode = locale === "ar" ? "ar" : "en";
  const router = useRouter();
  const [fullUrl, setFullUrl] = useState("");
  useEffect(() => {
    const currentUrl = `${window.location.origin}/${pageData.slug}`;
    setFullUrl(currentUrl);
  }, [pageData.slug]);

  return (
    <div>
      <section
        className={styles.subbanner}
        style={{
          backgroundImage: `url(${pageData.acf.banner_im})`,
          backgroundSize: "cover",
          backgroundPosition: "center",
          backgroundRepeat: "no-repeat",
        }}
      >
        <div className={`${styles.container} container`}>
          <div>
            <span className={styles.inner_banner_label}>
              {formatDate(pageData.date, langCode)}
            </span>
            <h1
              className={`${styles.inner_banner_title} text-white`}
              dangerouslySetInnerHTML={{ __html: pageData.title.rendered }}
            />
          </div>
        </div>
      </section>

      <div class="bg_main">
        <section
          className={`${styles.news_detail_container} pt-110 pb-80 bg-secondary`}
        >
          <div className={`${styles.news_container} container relative flex`}>
            <div
              className={`${styles.news_detail_left} text-white news_detail_left_ar`}
            >
              <span>{formatDate(pageData.date, langCode)}</span>
              <h2
                className="main_title text-white"
                dangerouslySetInnerHTML={{ __html: pageData.acf.title }}
              />
              {pageData.acf.detail.textarea && (
                <div
                  dangerouslySetInnerHTML={{
                    __html: pageData.acf.detail.textarea,
                  }}
                />
              )}
            </div>

            {pageData.acf.detail.image && (
              <div className={`${styles.news_detail_right} news_detail_right`}>
                <Image
                  src={pageData.acf.detail.image}
                  alt="image"
                  width={558}
                  height={744} loading='lazy'
                />
              </div>
            )}

            {pageData.acf.textarea2 && (
              <div
                className="pt-50"
                dangerouslySetInnerHTML={{ __html: pageData.acf.textarea2 }}
              />
            )}

            <div className={`${styles.shre_main} pt-50`}>
              <h3>
                {router.locale === "ar" ? "لمشاركة هذا الاعلان" : "Share This"}
              </h3>
              <ul>
                <li>
                  <a
                    href={`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(
                      fullUrl
                    )}`}
                    target="_blank"
                  >
                    <Image
                      src="/images/facebook.svg"
                      alt="image"
                      width={116}
                      height={43} loading='lazy'
                    />
                  </a>
                </li>

                <li>
                  <a
                    href={`https://twitter.com/intent/tweet?url=${encodeURIComponent(
                      fullUrl
                    )}`}
                    target="_blank"
                  >
                    <Image
                      src="/images/twitter.svg"
                      alt="image"
                      width={116}
                      height={43} loading='lazy'
                    />
                  </a>
                </li>

                <li>
                  <a
                    href={`https://pinterest.com/pin/create/button/?url=${encodeURIComponent(
                      fullUrl
                    )}`}
                    target="_blank"
                  >
                    <Image
                      src="/images/pin.svg"
                      alt="image"
                      width={116}
                      height={43} loading='lazy'
                    />
                  </a>
                </li>

                <li>
                  <a
                    href={`mailto:?subject=${encodeURIComponent(
                      "Check this out!"
                    )}&body=${encodeURIComponent(fullUrl)}`}
                    target="_blank"
                  >
                    <Image
                      src="/images/mail.svg"
                      alt="image"
                      width={116}
                      height={43} loading='lazy'
                    />
                  </a>
                </li>

                {/* <li>
									<Link href="#.">
										<Image
											src="/images/share.svg"
											alt="image"
											width={116}
											height={43}
										/>
									</Link>
								</li> */}

                {/* <li>
									<Link href="#.">
										<Image
											src="/images/share2.svg"
											alt="image"
											width={116}
											height={43}
										/>
									</Link>
								</li> */}
              </ul>
            </div>
          </div>
        </section>
        {related && related?.length > 0 && (
          <section className={`${styles.news_detail_related} pt-80 bg-secondary`}>
            <div className="container relative">
              <h2 class="main_title text-white mb-30">
                {langCode === 'ar' ? 'الأخبار والرؤى ذات الصلة' : 'Related News and Insights'}
              </h2>
              <div className={styles.news_list} data-aos="fade-up">
                {related &&
                  related.slice(0, 3).map((item) => (
                  <Newscard key={item.id} item={item} />
                ))}
              </div>
            </div>
          </section>
        )}
      </div>
    </div>
  );
}

export default Newsdetails;
export async function getServerSideProps(context) {
  const { params } = context;
  const slug = params.slug;
  const { locale } = context;
  const langCode = locale === "ar" ? "ar" : "en";
  try {
    const pageData = await fetchByPost("news", slug, langCode);

    // Pass only the first item of "news-type" if it exists
    const related = await fetchRelatedNews(
      langCode,
      "3",
      pageData.id,
      pageData["news-type"] ? pageData["news-type"][0] : null
    );

    if (!pageData) {
      return {
        notFound: true,
      };
    }
    return {
      props: {
        pageData,
        related,
      },
    };
  } catch (error) {
    console.error("Failed to fetch data:", error);
    return {
      notFound: true,
    };
  }
}
