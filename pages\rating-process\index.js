import React from "react";
import styles from "@/pages/rating-methodologies/ratingMethodologies.module.css";
import Pagemenu from "@/components/pagemenu/pagemenu";
import SubBanner from "@/components/innerbanner/innerbanner";

import { fetchPageBySlug } from "@/lib/api/PageBySlug";
import {fetchRatingTab} from "@/lib/api/byfield"
import Image from "next/image";



const Index = ({pageData, Tabs}) => {
	const banner = pageData.acf.banner_details || '';
	const data = pageData.acf.rating_methodologies || '';
	const taabs = pageData.acf.details_process || '';
	return (
		<>
			<SubBanner
				bannerDetails={banner}
				alignEnd={false}
			/>
			<Pagemenu menuItems={Tabs.tabs} />
			<section className="pt-120 bg-secondary center_bg">
				
				<div  data-aos="fade-up">
					<div className="container">
						<div className="text-white">
							<h2 className="main_title text-center mb-30" dangerouslySetInnerHTML={{ __html: pageData.acf.main_title }} />
						</div>
					
						<div className={styles.find_rating_image_wrapper}>
							<Image src={pageData.acf.process_image} alt=""  
							width={0}
							height={0}
							style={{ width: "100%", height: "auto" }}  loading='lazy'
							/>
						</div>
					</div>
				</div>
			</section>
		</>
	);
};

export default Index;

export async function getServerSideProps({locale}) {
	const langCode = locale === "ar" ? "ar" : "en";
	try {
		const pageData = await fetchPageBySlug("rating-process", langCode);
		const Tabs = await fetchRatingTab(langCode);
		return {
			props: {
				pageData,
				Tabs
			},
		};
	} catch (error) {
		console.error("Failed to fetch data:", error);
		return {
			props: {
				pageData: null
			},
		};
	}
}