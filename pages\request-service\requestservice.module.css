.sixcolumn {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--gap-24);
}

.inquiry_request {
    padding: 95px 0;
    background: #ffffff;
}

/* -------------------------------------------------------------- */

.inquiry_request_list {
    display: flex;
    flex-wrap: wrap;
    row-gap: 15px;
    margin: 0 -1%;
}

.inquiry_request_list li {
    width: 31.33%;
    margin: 0 1%;
}

.inquiry_request_link {
    height: 94px;
    padding: 0 10px;
    font-size: 1.625rem;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: #ffffff;
    background: rgba(0, 40, 85, 1) 100%;
    position: relative;
    z-index: 0;
}

.inquiry_request_link,
.inquiry_request_link::after {
    border-radius: 29px;
}

.inquiry_request_link::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(113.49deg,
            rgba(232, 89, 59, 1) 0%,
            rgba(0, 40, 85, 1) 100%);
    opacity: 1;
    transition: opacity .4s ease-in-out;
    z-index: -1;
}

.inquiry_request_link:hover::after {
    opacity: 0;
}

@media (max-width: 1200px) {
    .inquiry_request {
        padding: 80px 0;
    }

    .inquiry_request_link {
        height: 60px;
        font-size: 1rem;
    }
    .inquiry_request_link,
.inquiry_request_link::after {
    border-radius: 16px;
}
}

@media (max-width: 860px) {
    .inquiry_request {
        padding: 50px 0;
    }
}

@media (max-width: 600px) {
    .sixcolumn {
        grid-template-columns: repeat(1, 1fr);
    }

    .inquiry_request_list li {
        width: 100%;
    }

    .inquiry_request h3 {
        margin-bottom: 20px;
        text-align: center;
    }
    .sixcolumn {
        width:100%;
    }
}