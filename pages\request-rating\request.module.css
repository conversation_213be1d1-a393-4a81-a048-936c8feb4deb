.assessment_columns {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--gap-24);
}

/* ---------------------------------------------------- */

.dissemination_columns {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--gap-24);
}

/* ---------------------------------------------------- */
@media (max-width: 860px) {
    .dissemination_columns {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 600px) {
    .assessment_columns,
    .dissemination_columns {
        grid-template-columns: repeat(1, 1fr);
    }
}