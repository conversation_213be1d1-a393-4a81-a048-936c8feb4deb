import React, { useRef, useState } from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import Image from "next/image";
import "swiper/css";
import "swiper/css/effect-fade";
import { EffectFade, Autoplay } from "swiper/modules";
import styles from "./historyslider.module.css";

const Historyslider = ({HistoryList}) => {
	const swiperRef = useRef(null);
	const [activeIndex, setActiveIndex] = useState(0);

	const handleSlideChange = (index) => {
		if (swiperRef.current) {
			swiperRef.current.slideTo(index);
		}
	};

	return (
		<div className="" data-aos="fade-up">
			<ul className={styles.historyslider_thumb}>
				{HistoryList.map((slide, index) => (
					<li
						key={index}
						onClick={() => handleSlideChange(index)}
						className={activeIndex === index ? styles.active : ""}
					>
						{slide.year}
					</li>
				))}
			</ul>
			<Swiper
				spaceBetween={0}
				pagination={{
					clickable: true,
				}}
				effect={"fade"}
				loop={true}
				navigation={false}
				autoplay={{
					delay: 4500,
					disableOnInteraction: false,
				}}
				modules={[EffectFade, Autoplay]}
				className="historyslider"
				onSwiper={(swiper) => (swiperRef.current = swiper)}
				onSlideChange={(swiper) => setActiveIndex(swiper.realIndex)} // Update realIndex on slide change
			>
				{HistoryList.map((slide, index) => (
					<SwiperSlide key={index}>
						<div className={styles.historyslider_item}>
							<div className={styles.historyslider_content}>
								<h3 className="main_title" dangerouslySetInnerHTML={{ __html: slide.title }} />
								{slide.description.map((des,subindex) => {
									return (<p key={subindex} dangerouslySetInnerHTML={{ __html: des.text }}  />)
								})}
							</div>
							<div className={`${styles.historyslider_image} zoom-effect`}>
								<Image
									src={slide.image}
									alt={`history image ${index + 1}`}
									width={737}
									height={508}
									priority
								/>
							</div>
						</div>
					</SwiperSlide>
				))}
			</Swiper>
		</div>
	);
};

export default Historyslider;
