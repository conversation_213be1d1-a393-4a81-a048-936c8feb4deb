.table_style {
    width: 100%;
    height: auto;
    border-collapse: collapse;
}

.table_style thead th {
    color: #ffffff;
    padding-bottom: 40px;
    font-size: 1.125rem;
    font-weight: 400;
    font-family: 'precioussanstwo-medium';
    text-align: left;
    min-width: 100px;
}

:global(body.rtl) .table_style thead th{
    text-align: right;
}

.table_style tbody tr {
    border-top: 2px solid #B4B4B4;
}

.table_style tbody td:first-child {
    font-size: 1.125rem;
    font-weight: 400;
    font-family: 'precioussanstwo-medium';
}

:global(body.rtl)  .table_style tbody td:first-child {
    font-family: var(--font-arabic);
}

.table_style td {
    color: #ffffff;
    padding: 40px 0;
}

/* .table_style tr:last-child td {
    padding-bottom:0;
} */

.pdf_link {
    display: flex;
    align-items: center;
    column-gap: 8px;
    text-transform: uppercase;
}

@media (max-width: 1200px) {
    .table_style td {
        padding: 25px 0;
    }

    .table_style thead th {
        padding-bottom: 20px;
    }
}

@media (max-width: 860px) {
    .table_style td {
        padding: 15px 0;
    }

    .table_style tbody td {
        padding-right: 5px;
    }

    .table_style tbody td:first-child {
        font-size: 1rem;
    }
}

@media (max-width: 860px) {
    .table_style td {
        padding: 10px 0;
    }
    
    .table_style tbody tr {
        border-top: 1px solid #B4B4B4;
    }
}
@media (max-width: 767px) {

}