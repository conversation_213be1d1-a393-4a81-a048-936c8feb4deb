.header {
  position: fixed;
  top: 0;
  width: 100%;
  background-color: transparent;
  z-index: 9;
  transition: all 0.3s ease-in-out;
}

.logo,
.logo img {
  transition: all 0.3s ease-in-out;
}

.top_header {
  padding: 6px 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #858383;
}

.top_header_ul {
  --space: 18px;
  margin-left: auto;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
}

.top_header_ul a {
  display: flex;
  align-items: center;
  column-gap: 4px;
}



.top_header_ul li:last-child {
  padding-left: var(--space);
  margin-left: var(--space);
  border-left: 1px solid #a0a0a0;
  font-family: var(--font-arabic);
}

.top_header_ul a {
  color: #ffffff;
}

.top_header_ul a:hover {
  color: var(--color-primary);
}

.top_header_ul_ar {
  --space: 18px;
  margin-right: auto;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
}

.top_header_ul_ar a {
  display: flex;
  align-items: center;
  column-gap: 4px;
}

.top_header_ul_ar a > img {
  margin-left: 5px;
}

.top_header_ul_ar li:last-child {
  padding-right: var(--space);
  margin-right: var(--space);
  border-right: 1px solid #a0a0a0;
}

.top_header_ul_ar li:first-child a{
  font-family: var(--font-arabic);
}

.top_header_ul_ar li:nth-child(2) a{
  font-family: var(--font-primary);
}

.top_header_ul_ar a {
  color: #ffffff;
}

.top_header_ul_ar a:hover {
  color: var(--color-primary);
}

/* ---------------------------------------------------------------------- */

.main_header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 0;
  position: relative;
}

.header_nav {
  display: flex;
  align-items: center;
  column-gap: 47px;
}

/* ---------------------------------------------------------------------- */
.menu_ul {
  display: flex;
  gap: 28px;
  align-items: center;
}

.menu_ul li a {
  font-size: 1rem;
  color: #fff;
  text-transform: capitalize;
  transition: all 0.3s ease-in-out;
}

:global(body.rtl) .menu_ul li a {
  font-family: var(--font-arabic);
}

.menu_ul li:hover > a,
.menu_ul li.active > a {
  color: var(--color-primary);
}
/* .menu_ul li.active>a {
  color: var(--color-primary);
} */

/* .menu_ul>li+li {
  padding-left: 24px;
} */

.menu_ul > li {
  display: flex;
  gap: 10px;
  align-items: center;
}

/* ---------------------------------------------------------------------- */

.sticky {
  background-color: rgba(0, 0, 0, 0.95);
  padding-top: 0;
  z-index: 10;
}

/* ---------------------------------------------------------------------- */

.extra_menu {
  display: flex;
  align-items: center;
  column-gap: 8px;
}

.mobile_menu.extra_menu {
  display: none;
  opacity: 0;
  visibility: hidden;
}

.find_rating {
  display: flex;
  align-items: center;
  color: #ffffff;
  height: 45px;
  padding: 0 27px;
  background: var(--color-primary);
  border-radius: 100px;
}

:global(body.rtl) .find_rating {
  font-family: var(--font-arabic);
}

.request_rating {
  display: flex;
  align-items: center;
  column-gap: 4px;
  border: 2px solid var(--color-primary);
  color: #ffffff;
  height: 45px;
  padding: 0 27px;
  border-radius: 100px;
}

:global(body.rtl) .request_rating {
  font-family: var(--font-arabic);
}

.request_rating:hover {
  background: var(--color-primary);
}

/* ---------------------------------------------------------------------- */

.sub_menu {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  background: rgba(19, 51, 130, 0.4);
  padding: 32px 60px;
  backdrop-filter: blur(25px);
  border-radius: 0 0 var(--radius-34) var(--radius-34);
  visibility: hidden;
  opacity: 0;
  transform: translateY(50px);
  transition: opacity 0.4s ease-in-out, visibility 0.4s ease-in-out,
    transform 0.4s ease-in-out;
}

.submenu_arw {
  /* margin-left: 7px; */
  cursor: pointer;
  display: inline-block;
  transition: transform 0.4s ease-in-out;
}

.menu_ul li:hover .sub_menu {
  visibility: visible;
  opacity: 1;
  transform: translateY(10px);
}

.menu_ul li:hover .submenu_arw {
  transform: rotate(180deg);
}

/* ---------------------------------------------------------------------- */
.mega_menu {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  background: rgba(19, 51, 130, 0.4);
  padding: 32px 60px;
  backdrop-filter: blur(25px);
  border-radius: 0 0 var(--radius-34) var(--radius-34);
  visibility: hidden;
  opacity: 0;
  transform: translateY(50px);
  transition: opacity 0.4s ease-in-out, visibility 0.4s ease-in-out,
    transform 0.4s ease-in-out;
  z-index: 1;


}

@media (max-width: 767px) {
  .mega_menu {
  backdrop-filter: blur(0);
  border-radius: 0;
  transition: opacity 0.2s ease-in-out, visibility 0.2s ease-in-out, transform 0.2s ease-in-out;
  }
}

.mega_menu_column {
  display: flex;
  flex-flow: row wrap;
}

.mega_menu_title_desc {
  width: 31%;
  color: #ffffff;
  padding-right: 30px;
}

.mega_menu_title_desc h4 {
  font-size: 2.375rem;
  font-weight: 400;
  line-height: 1;
  margin-bottom: 15px;
}

.mega_menu_title_desc p {
  font-size: 0.75rem;
  font-weight: 300;
  margin: 0;
  line-height: 20px;
}

.mega_menu_column ul {
  display: flex;
  width: 60%;
  flex-flow: row wrap;
  justify-content: space-between;
  padding-left: 30px;
  border-left: 1px solid #6e7da4;
}

.mega_menu_column_ar {
  display: flex;
  flex-flow: row wrap;
}

.mega_menu_column_ar ul {
  display: flex;
  width: 60%;
  flex-flow: row wrap;
  justify-content: space-between;
  padding-right: 30px;
  border-right: 1px solid #6e7da4;
  margin-right: 10px;
}

.mega_menu_column li {
  width: calc(50% - 20px);
}

.mega_menu_column_ar li {
  width: calc(50% - 20px);
}

.mega_menu ul li {
  margin-bottom: 8px;
}

.mega_menu ul li a {
  display: block;
  color: #ffffff;
  font-weight: 400;
  padding: 8px 10px 8px 22px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.mega_menu ul li a:hover {
  color: var(--color-primary);
}

.menu_ul li:hover .mega_menu {
  visibility: visible;
  opacity: 1;
  transform: translateY(10px);
}

/* ---------------------------------------------------------------------- */

.menu_icon {
  width: 32px;
  height: 24px;
  z-index: 9;
  position: relative;
  cursor: pointer;
  display: none;
  /* margin-left: auto; */
  -webkit-transform: rotate(0);
  transform: rotate(0);
  -webkit-transition: all var(--time) ease-in-out;
  transition: all var(--time) ease-in-out;
}

.menu_icon span {
  display: block;
  position: absolute;
  height: 3px;
  width: 100%;
  background: #ffffff;
  border-radius: 4px;
  opacity: 1;
  left: 0;
  -webkit-transform: rotate(0);
  transform: rotate(0);
  -webkit-transition: all var(--time) ease-in-out;
  transition: all var(--time) ease-in-out;
}

.menu_icon span:nth-child(1) {
  top: 0;
}

.menu_icon span:nth-child(2),
.menu_icon span:nth-child(3) {
  top: 10px;
}

.menu_icon span:nth-child(4) {
  top: 20px;
}

.menu_icon.open span:nth-child(1) {
  top: 10px;
  width: 0;
  left: 50%;
}

.menu_icon.open span:nth-child(2) {
  -webkit-transform: rotate(45deg);
  transform: rotate(45deg);
}

.menu_icon.open span:nth-child(3) {
  -webkit-transform: rotate(-45deg);
  transform: rotate(-45deg);
}

.menu_icon.open span:nth-child(4) {
  top: 10px;
  width: 0;
  left: 50%;
}

.menu_icon.open span {
  background: var(--color-primary);
}

.menu_icon_ar {
  width: 32px;
  height: 24px;
  z-index: 9;
  position: relative;
  cursor: pointer;
  display: none;
  /* margin-left: auto; */
  -webkit-transform: rotate(0);
  transform: rotate(0);
  -webkit-transition: all var(--time) ease-in-out;
  transition: all var(--time) ease-in-out;
}

.menu_icon_ar span {
  display: block;
  position: absolute;
  height: 3px;
  width: 100%;
  background: #ffffff;
  border-radius: 4px;
  opacity: 1;
  left: 0;
  -webkit-transform: rotate(0);
  transform: rotate(0);
  -webkit-transition: all var(--time) ease-in-out;
  transition: all var(--time) ease-in-out;
}

.menu_icon_ar span:nth-child(1) {
  top: 0;
}

.menu_icon_ar span:nth-child(2),
.menu_icon_ar span:nth-child(3) {
  top: 10px;
}

.menu_icon_ar span:nth-child(4) {
  top: 20px;
}

.menu_icon_ar.open span:nth-child(1) {
  top: 10px;
  width: 0;
  left: 50%;
}

.menu_icon_ar.open span:nth-child(2) {
  -webkit-transform: rotate(45deg);
  transform: rotate(45deg);
}

.menu_icon_ar.open span:nth-child(3) {
  -webkit-transform: rotate(-45deg);
  transform: rotate(-45deg);
}

.menu_icon_ar.open span:nth-child(4) {
  top: 10px;
  width: 0;
  left: 50%;
}

.menu_icon_ar.open span {
  background: var(--color-primary);
}


@media (max-width: 1200px) {
  .logo {
    margin-right: auto;
  }

  .logo_ar {
    margin-left: auto;
  }

  .menu_icon {
    display: block;
    margin-left: 30px;
  }

  .menu_icon_ar {
    display: block;
    margin-right: 30px;
  }

  .main_header {
    justify-content: flex-end;
  }

  .navbar {
    position: fixed;
    width: 100%;
    z-index: 8;
    background: #fff;
    top: 0;
    height: 0;
    left: 0;
    right: 0;
    overflow: hidden;
    align-items: center;
    display: flex;
    flex-wrap: wrap;
    -webkit-transition: all var(--time) ease-in-out;
    transition: all var(--time) ease-in-out;
  }

  .header .slow {
    height: 100vh;
    padding-top: 120px;
  }

  .menu_ul {
    display: flex;
    flex-wrap: wrap;
    width: 100%;
    margin: 0;
    padding: 0 4%;
    flex-direction: column;
    align-items: flex-start;
  }

  .menu_ul li {
    padding: 0;
    margin: 0 var(--space);
    justify-content: space-between;
    width: 100%;
  }

  .menu_ul li + li {
    padding: 0;
    margin-left: 0;
    margin-top: 25px;
  }

  .menu_ul a {
    font-size: 20px;
    color: #000;
    font-weight: bold;
  }

  .mobile-icon {
    position: absolute;
    right: 0;
    top: 0;
  }

  .header li .mobile-icon.active {
    transform: scaleY(-1);
  }

  .menu_ul li a {
    color: #000000;
  }

  /* ---------------------------------------------- */
  .mega_menu {
    padding: 30px;
    background: rgb(19 51 130);
    transform: translateY(15px);
  }
  .menu_ul li:hover .mega_menu {
    visibility: hidden;
    opacity: 1 ;
    transform: unset ;
  }
 
  .mega_menu_column {
    flex-direction: column;
  }

  .mega_menu_column_ar {
    flex-direction: column;
  }

  .mega_menu_title_desc h4 {
    font-size: 1.5rem;
    margin-bottom: 10px;
  }

  .menu_ul {
    row-gap: 15px;
  }

  .mega_menu_column ul {
    padding: 0;
    border: none;
    width: 100%;
    margin-top: 30px;
  }

  .mega_menu_column_ar ul {
    padding: 0;
    border: none;
    width: 100%;
    margin-top: 30px;
  }

  .menu_ul li + li {
    margin: 10px 0 0 0;
  }

  .mega_menu_title_desc {
    padding: 0;
    width: 100%;
  }

  .mega_menu ul li a {
    padding: 0 0 10px 0;
  }

  .submenu_arw {
    /* margin-left: auto;
    float: right; */
    text-align: center;
    width: 30px;
  }

  .submenu_arw img {
    filter: invert();
    width: 20px;
  }
  .menu_drop_li{
  position: relative;
}


body.nav-open {
  height: 100vh;
  overflow: hidden;
}

.active .mega_menu {
  position: absolute;
  visibility: visible !important;
  opacity: 1;
  transform: translateY(10px) !important;
}

.menu_ul li:hover .submenu_arw {
  transform: unset ;
}
.active > span {
  transform: rotate(180deg) !important;
}

}

@media (max-width: 600px) {
  .extra_menu {
    display: none;
  }

  .mobile_menu.extra_menu {
    display: flex;
    visibility: visible;
    opacity: 1;
    width: 100%;
    /* justify-content: center; */
    padding: 35px 4% 0px;
  }

  .request_rating {
    color: var(--color-primary);
  }

  .request_rating img {
    filter: invert(62%) sepia(39%) saturate(5545%) hue-rotate(336deg)
      brightness(113%) contrast(72%);
  }

  .mega_menu_column li {
    width: 100%;
  }

  .mega_menu_column ul {
    margin-top: 20px;
  }

  .mega_menu_column_ar li {
    width: 100%;
  }

  .mega_menu_column_ar ul {
    margin-top: 20px;
  }

  .mega_menu {
    padding: 20px;
    border-radius: 10px;
  }

  .mega_menu_column li:last-child a {
    padding: 0;
    border: none;
  }

  .mega_menu_column_ar li:last-child a {
    padding: 0;
    border: none;
  }

  .menu_icon.open {
    margin-top: -75px;
  }

  .navbar {
    flex-direction: column;
    justify-content: flex-start;
  }

  .menu_ul {
    row-gap: 10px;
    /* height: 400px;
    overflow: hidden;
    overflow-y: scroll; */
  }

  .mega_menu {
    transform: none;
    transition: none;
  }

  /* .menu_ul li{
    flex-wrap: wrap;
  } */

  /* .active .mega_menu{
    position: static !important;
  } */
}

