import React from "react";
import Accordion from "@/components/accordion/accordion";
import SubBanner from "@/components/innerbanner/innerbanner";
import { fetchPageBySlug } from "@/lib/api/PageBySlug";

const Index = ({pageData}) => {
	const banner = pageData.acf.banner_details || '';
	return (
		<>
			<SubBanner
				bannerDetails={banner}
				alignEnd={false}
			/>

			<section className="pt-120 bg-secondary">
				<div className="container">
					<p className="color-pera mb-50"
						dangerouslySetInnerHTML={{ __html: pageData.acf.faq_details.description }}
					/>
					<Accordion list={pageData.acf.faq_details.faqs} />
				</div>
			</section>
		</>
	);
};

export default Index;
export async function getServerSideProps({locale}) {
	const langCode = locale === "ar" ? "ar" : "en";
	try {
		const pageData = await fetchPageBySlug("faqs", langCode);
		return {
			props: {
				pageData
			},
		};
	} catch (error) {
		console.error("Failed to fetch data:", error);
		return {
			props: {
				pageData: null
			},
		};
	}
}