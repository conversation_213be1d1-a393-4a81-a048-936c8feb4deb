import React from 'react';
import Image from "next/image";
import styles from "./links.module.css";

const ImportLink = ({ importlinks }) => {
    return (
        <>


            {importlinks.map((links, index) => (
                <div key={index} className={styles.profile_wrap}>
                    <div className={styles.link_image}>
                        <Image
                            src={links.image}
                            alt={links.title || "Profile Image"} // Use a meaningful alt text
                            width={325}
                loading='lazy'
                            height={360}
                        />
                    </div>

                    <div className={styles.link_content}>
                        <h3>{links.title}</h3> {/* Use the title for the team member's name */}
                    </div>
                </div>
            ))}

        </>
    );

};

export default ImportLink