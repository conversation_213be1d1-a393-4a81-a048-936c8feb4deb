import React from "react";
import styles from "./ratingPolicies.module.css";
import Pagemenu from "@/components/pagemenu/pagemenu";
import SubBanner from "@/components/innerbanner/innerbanner";
import Tablestyle from "@/components/tablestyle/tablestyle";
import { fetchPageBySlug } from "@/lib/api/PageBySlug";
import {fetchRatingTab} from "@/lib/api/byfield"


const Index = ({pageData, Tabs}) => {
	const banner = pageData.acf.banner_details || '';
	return (
		<>
			<SubBanner
				bannerDetails={banner}
				alignEnd={false}
			/>
			<Pagemenu menuItems={Tabs.tabs} />
			<section className="pt-120 bg-secondary center_bg">
				<div className="container">
					<p className="color-pera mb-50" data-aos="fade-up"
						dangerouslySetInnerHTML={{ __html: pageData.acf.policies.description }}
					/>
					<div data-aos="fade-up">
						<Tablestyle
							documents={pageData.acf.policies.list}
							className={styles.table_two_column}
						/>
					</div>
				</div>
			</section>
		</>
	);
};

export default Index;
export async function getServerSideProps({locale}) {
	const langCode = locale === "ar" ? "ar" : "en";
	try {
		const pageData = await fetchPageBySlug("rating-policies", langCode);
		const Tabs = await fetchRatingTab(langCode);
		return {
			props: {
				pageData,
				Tabs
			},
		};
	} catch (error) {
		console.error("Failed to fetch data:", error);
		return {
			props: {
				pageData: null
			},
		};
	}
}