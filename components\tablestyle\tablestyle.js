import React from "react";
import Image from "next/image";
import { useRouter } from "next/router";
import styles from "./tablestyle.module.css";

const Tablestyle = ({ className, documents, regulatory }) => {
    const { pathname } = useRouter();
    const { locale } = useRouter();
  const langCode = locale === "ar" ? "ar" : "en";
    // const handleDownload = async (url, filename) => {
    //     try {
    //         const response = await fetch(url, {
    //             method: 'GET',
    //             headers: {
    //                 'Content-Type': 'application/pdf',
    //             },
    //             mode: 'no-cors', 
    //         });
    
    //         const blob = await response.blob();
    //         const link = document.createElement('a');
    //         link.href = URL.createObjectURL(blob);
    //         link.download = filename;
    //         document.body.appendChild(link);
    //         link.click();
    //         document.body.removeChild(link);
    //     } catch (error) {
    //         console.error('Download failed:', error);
    //     }
    // };
    
	

    return (
        <table className={`${styles.table_style} ${className ? className : ""}`}>
            {pathname === "/regulatory-reports" && (
                <thead>
                    <tr>
                        <th>{langCode==='ar' ? 'التاريخ' : 'Date'}</th>
                        <th>{langCode==='ar' ? 'الوصف' : 'Description'}</th>
                        <th>{langCode==='ar' ? 'تنزيل' : 'Download'}</th>
                    </tr>
                </thead>
            )}
            <tbody>
                {documents.map((doc, index) => (
                    <tr key={index}>
                        {doc.date_ && (
                            <td>{doc.date_}</td>
                        )}
                        
                        {doc.description && (
                            <td>
                                {doc.description ? doc.description : langCode==='ar' ? '' : "لا يوجد وصف متاح"}
                            </td>
                        )}
                         {regulatory && (
                            <td>
                                {doc.type ? doc.type.label : langCode==='ar' ? '' : "لا يوجد وصف متاح"}
                            </td>
                        )}
                        <td>
                        <a
                            style={{ cursor: 'pointer' }}
                            href={doc.report.url}
                            // onClick={() => handleDownload(doc.report.url, doc.report.filename)}
                            className={`${styles.pdf_link} text-primary`}
                            download
                            target="_blank"
                        >
                                PDF
                                <Image
                                    src="/images/dowload-primary.svg"
                                    alt="download icon"
                                    width={15}
                                    height={15}
                                    priority
                                />
                            </a>
                        </td>
                    </tr>
                ))}
            </tbody>
        </table>
    );
};

export default Tablestyle;
