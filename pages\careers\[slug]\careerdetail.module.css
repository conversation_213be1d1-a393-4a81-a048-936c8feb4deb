.careers_top_section {
    width: 100%;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    padding-bottom:60px;
}

.careers_detail_section {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
}

.careers_detail_title ul {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
}

.careers_detail_title p {
    color: #D7D7D7;
}

.careers_detail_title ul li {
    font-size: 1rem;
    line-height: 26px;
    color: #D7D7D7;
}

.careers_detail_title ul li+li {
    padding-left: 30px;
    margin-left: 30px;
    position: relative;
}

.careers_detail_title ul li+li::after {
    content: "";
    width: 2px;
    height: 20px;
    position: absolute;
    left: 0;
    top: 3px;
    background-color: #fff;
}

.career_detail_content_wrap {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
}

.career_detail_content_left {
    width: 65%;
    padding-right: 130px;
    border-right: 1px solid rgba(255, 255, 255, 0.2);
}

.career_detail_content_right {
    width: 35%;
    padding-left: 120px;
}

.career_detail_box {
    margin-bottom: 45px;
}

.career_detail_box h3 {
    font-family: 'precioussanstwo-bold';
    font-size: 1.5625rem;
    line-height: 28px;
    color: #fff;
    margin-bottom: 25px;
}

.career_detail_box ul {
    display: flex;
    flex-wrap: wrap;
    row-gap: 20px;
}

.career_detail_box ul li {
    color: #D7D7D7;
    font-size: 1rem;
    line-height: 26px;
    position: relative;
    padding-left: 25px;
    width: 100%;
}

.career_detail_box ul li::after {
    content: "";
    width: 13px;
    height: 13px;
    background-image: url(../../../public//images/tick.svg);
    background-repeat: no-repeat;
    position: absolute;
    left: 0px;
    top: 5px;
    background-size: 13px;
}

.career_detail_box ul li span {
    display: block;
    width: 100%;
}

@media (max-width: 1200px) {
    .career_detail_content_left {
        padding-right: 50px;
    }

    .career_detail_content_right {
        padding-left: 50px;
    }
}

@media (max-width: 860px) {
    .career_detail_content_left {
        padding-right: 25px;
    }

    .career_detail_content_right {
        padding-left: 25px;
    }
}

@media (max-width: 767px) {

    .career_detail_content_left,
    .career_detail_content_right {
        width: 100%;
        padding: 0;
        border: none;
    }

    .career_detail_box ul {
        row-gap: 15px;
    }

    .career_detail_box h3 {
        font-size: 1.25rem;
    }
}