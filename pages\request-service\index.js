import React from "react";
import pagestyle from "./requestservice.module.css";
import styles from "@/pages/dashboard/myaccount.module.css";
import Sidemenu from "@/components/sidemenu/sidemenu";
import Sixcolumn from "@/components/sixcolumn/sixcolumn";
import Link from "next/link";
import ProtectedRoute from "@/components/ProtectedRoute";
import { fetchPageBySlug, fetchServices } from "@/lib/api/PageBySlug";
import { useRouter } from "next/router";

const Index = ({pageData, serviceList}) => {
	const { locale } = useRouter(); 

	return (
		<>
		<ProtectedRoute>
			<div className={`${styles.my_account} py-120 bg-secondary`}>
				<div className={`container`}>
					<h2 className={`${styles.title} main_title text-white text-center`}>
						{locale=== 'ar' ? 'حسابي' : "My Account" }
					</h2>
					<div className={styles.container_inside}>
						<div className={styles.left_navigation}>
							<h4>{locale=== 'ar' ? 'حسابي' : "My Account" }</h4>
							<Sidemenu />
						</div>
						<div className={styles.my_account_right}>
						<h4>{locale=== 'ar' ? 'طلب خدمة' : 'Request a Service'}</h4>
							<Sixcolumn
								sixColumnItems={serviceList}
								list={true}
								className={pagestyle.sixcolumn}
							/>
						</div>
					</div>
				</div>
			</div>

			<section className={pagestyle.inquiry_request}>
				<div className="container">
					<h3 className="text-secondary main_title mb-30">Inquiry Request</h3>
					<ul className={pagestyle.inquiry_request_list}>
						<li>
							<Link href="#." className={pagestyle.inquiry_request_link}>
								Rating Inquiry
							</Link>
						</li>
						<li>
							<Link href="#." className={pagestyle.inquiry_request_link}>
								{" "}
								Detailed Credit Rating Report
							</Link>
						</li>
						<li>
							<Link href="#." className={pagestyle.inquiry_request_link}>
								Research Paper Requests.
							</Link>
						</li>
					</ul>
				</div>
			</section>
		</ProtectedRoute>
		</>
	);
};

export default Index;

export async function getServerSideProps({locale}) {
	const langCode = locale === "ar" ? "ar" : "en";
	try {
		const pageData = await fetchPageBySlug("services", langCode);
		const serviceList = await fetchServices(langCode)
		return {
			props: {
				pageData,
				serviceList
			},
		};
	} catch (error) {
		console.error("Failed to fetch data:", error);
		return {
			props: {
				pageData: null
			},
		};
	}
}
