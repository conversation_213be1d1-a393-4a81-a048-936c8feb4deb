import React, { useEffect, useState } from "react";
import styles from "./dashboard.module.css";
import { useRouter } from "next/router"; 
import { fetchPageBySlug, fetchRequest } from "@/lib/api/PageBySlug";

const Dashboard = ({ langCode}) => {
	const [pageData, setPageData] = useState(null);
	const [latestRequest, setLatestRequest] = useState([]);
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState(null);
	const [userId, setUserId] = useState(null); 
	const router = useRouter(); 
 	langCode=router.locale;
	
	useEffect(() => {
		const user = JSON.parse(localStorage.getItem("user"));
		const fetchData = async () => {
			try {
				setLoading(true); 
				const data = await fetchPageBySlug("dashboard",langCode);
				const requestList = await fetchRequest(langCode, "1", user );
				setPageData(data);
				setLatestRequest(requestList[0]);
				setLoading(false); 
			} catch (error) {
				setError("Failed to fetch data. Please try again later.");
				setLoading(false); 
				console.error("Failed to fetch data:", error);
			}
		};

		fetchData();
	}, [langCode]);

	if ( !pageData ) {
		return <div className={styles.loading}></div>;
	}
//console.log(pageData)

	return (
		<>
			{pageData.acf.dasboard_content.map((card, index) => (
				
					<div
						key={index}
						className={styles.card}
						style={{ backgroundImage: `url(${card.icon})` }}
					>
						<a href={card.link} >
						<div className={styles.card_inside}>
							<h4>{card.title}</h4>
							{card.description &&
								latestRequest &&
								latestRequest.acf &&
								latestRequest.acf.amount &&
								latestRequest.payment_status_taxonomy &&
								latestRequest.payment_status_taxonomy.term_name && (
									<p>
										{card.description} {latestRequest.acf.amount} - {latestRequest.payment_status_taxonomy.term_name}
									</p>
								)}

						</div>
						</a>
					</div>
				
			))}
		</>
	);
};

export default Dashboard;
