import { useState, useEffect } from "react";
import Image from "next/image";
import Link from "next/link";
import styles from "./careerlist.module.css";

const CareerList = ({ careerlist }) => {

	const getRelativeTime = (date) => {
		const now = new Date();
		const secondsDiff = Math.floor((now - date) / 1000);
		const minutesDiff = Math.floor(secondsDiff / 60);
		const hoursDiff = Math.floor(minutesDiff / 60);
		const daysDiff = Math.floor(hoursDiff / 24);
		const monthsDiff = Math.floor(daysDiff / 30);
		const yearsDiff = Math.floor(daysDiff / 365);
	
		if (secondsDiff < 60) {
			return secondsDiff === 1 ? "1 second ago" : `${secondsDiff} seconds ago`;
		} else if (minutesDiff < 60) {
			return minutesDiff === 1 ? "1 minute ago" : `${minutesDiff} minutes ago`;
		} else if (hoursDiff < 24) {
			return hoursDiff === 1 ? "1 hour ago" : `${hoursDiff} hours ago`;
		} else if (daysDiff < 30) {
			return daysDiff === 1 ? "1 day ago" : `${daysDiff} days ago`;
		} else if (monthsDiff < 12) {
			return monthsDiff === 1 ? "1 month ago" : `${monthsDiff} months ago`;
		} else {
			return yearsDiff === 1 ? "1 year ago" : `${yearsDiff} years ago`;
		}
	};
	return (
		<div className={styles.career_list_wrap}>
			<ul>
				{careerlist.map((career, index) => {
				const dateObj = new Date(career.date);
				const relativeTime = getRelativeTime(dateObj);
				return (

					<li key={index} data-aos="fade-up">
						<div className={styles.career_list_title}>
							<div className={styles.career_list_icon}>
								<Image
									src="/images/career.png"
									alt="career"
									width={44}
									height={36}
                loading='lazy'

								/>
							</div>

							<div className={styles.career_list_new}>
								<h3 dangerouslySetInnerHTML={{ __html: career.title.rendered }}  />
								<p> Posted: {relativeTime} </p>
							</div>
						</div>

						<div className={styles.career_list_details}>
							<div className={styles.career_list_content}>
								<p  dangerouslySetInnerHTML={{ __html: career.acf.basic_details.short_description }}  />
							</div>
							<Link
								class={`${styles.career_btn} btn_style_wrap`}
								href={`careers/${career.slug}`}
							>
								<span class="btn_style_primary">Apply Now</span>
								<span class="btn_style_arrow">
									<Image
                loading='lazy'
										alt="icon"
										width="22"
										height="15"
										src="/images/white_right_arw.svg"
									/>
								</span>
							</Link>
						</div>
					</li>
				)}
				)}
			</ul>
		</div>
	);
};

export default CareerList;
