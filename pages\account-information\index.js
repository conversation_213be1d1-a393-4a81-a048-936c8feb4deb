import React from "react";
import styles from "@/pages/dashboard/myaccount.module.css";
import Sidemenu from "@/components/sidemenu/sidemenu";
import Information from "@/components/information/information";
import ProtectedRoute from "@/components/ProtectedRoute";
import { fetchPageBySlug } from "@/lib/api/PageBySlug";

const Index = ({pageData}) => {
	return (
		<ProtectedRoute>
			<div style={{width:'100%'}}>
				<Information data={pageData} />
			</div>
		</ProtectedRoute>
	
	);
};

export default Index;
export async function getStaticProps() {
	try {
		const pageData = await fetchPageBySlug("account-information");
		return {
			props: {
				pageData,
			},
			revalidate: 10, 
		};
	} catch (error) {
		console.error("Failed to fetch data:", error);
		return {
			props: {
				pageData: null,
			},
		};
	}
}
