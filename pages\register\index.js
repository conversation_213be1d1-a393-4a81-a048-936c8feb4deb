import React, { useState } from "react";
import Link from "next/link";
import Image from "next/image";
import styles from "@/styles/login.module.css";
import { useRouter } from "next/router";

const Index = () => {
  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");
  const [company, setCompany] = useState("");
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [message, setMessage] = useState("");
  const [error, setError] = useState("");
  const [membershipType, setMembershipType] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  const [isOpen, setIsOpen] = useState(false);

  const router = useRouter();
  const { locale } = router;

  const langCode = locale === "ar" ? "ar" : "en";

  const handleSubmit = async (e) => {
    e.preventDefault();

    setMessage("");
    setError("");
    if (passwordError == "") {
      setIsSubmitting(true);
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/register`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            first_name: firstName,
            last_name: lastName,
            company: company,
            email: email,
            password: password,
            membership_type: membershipType,
          }),
        }
      );

      const data = await response.json();
      setIsSubmitting(false);

      if (response.ok) {
        setMessage("Registration successful!");
        setFirstName("");
        setLastName("");
        setCompany("");
        setEmail("");
        setPassword("");
        setMembershipType("");
        setTimeout(() => {
          setMessage("");
          router.push("/login");
        }, 3000);

      } else {
        setError(data.message || "Registration failed. Please try again.");
        setTimeout(() => {
          setError("");
        }, 3000);
      }
    }
  };
  const [showPassword, setShowPassword] = useState(false);
  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  const [passwordError, setPasswordError] = useState("");

  const validatePassword = (password) => {
    // Password validation conditions
    const minLength = password.length >= 8;
    const hasUppercase = /[A-Z]/.test(password);
    const hasLowercase = /[a-z]/.test(password);
    const hasNumber = /[0-9]/.test(password);
    const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);

    // Set validation error if password doesn't meet criteria
    if (!minLength) {
      setPasswordError("Password must be at least 8 characters long");
    } else if (!hasUppercase) {
      setPasswordError("Password must contain at least one uppercase letter");
    } else if (!hasLowercase) {
      setPasswordError("Password must contain at least one lowercase letter");
    } else if (!hasNumber) {
      setPasswordError("Password must contain at least one number");
    } else if (!hasSpecialChar) {
      setPasswordError("Password must contain at least one special character");
    } else {
      setPasswordError("");
    }

    return (
      minLength && hasUppercase && hasLowercase && hasNumber && hasSpecialChar
    );
  };

  const handlePasswordChange = (e) => {
    const newPassword = e.target.value;
    setPassword(newPassword);
    validatePassword(newPassword);
  };

  const handleFocus = () => {
    setIsOpen(true);
  };

  const handleBlur = () => {
    setIsOpen(false);
  };

  return (
    <section className={`${styles.login_page} bg-secondary`}>
      <div className={`${styles.container} container`}>
        <div className={styles.login_wrap}>
          <div className={`${styles.login_title} text-center text-white`}>
            <h1 className="main_title mb-20">{langCode==="ar" ? "التسجيل" : "Register" }</h1>
            <p className="color-pera">{langCode==="ar" ? "يرجى ملء المعلومات أدناه:" : "Please fill in the information below:" }</p>
          </div>
          <div className={styles.login_body}>
            <form onSubmit={handleSubmit}>
              <ul>
                <li>
                  <input
                    type="text"
                    placeholder={langCode === "ar" ? "الاسم الأول*" : "First Name *"}
                    value={firstName}
                    onChange={(e) => setFirstName(e.target.value)}
                    required
                  />
                </li>
                <li>
                  <input
                    type="text"
              placeholder={langCode === "ar" ? "اسم العائلة*" : "Last Name *"}
                    value={lastName}
                    onChange={(e) => setLastName(e.target.value)}
                    required
                  />
                </li>
                <li>
                  <div
                    className={
                      langCode === "en"
                        ? styles.select_wrapper
                        : styles.select_wrapper_ar
                    }
                  >
                    <select
                      value={membershipType}
                      onChange={(e) => setMembershipType(e.target.value)}
                      required
                      style={{ width: "100%" }}
                      className="membership-select"
                      onFocus={handleFocus}
                      onBlur={handleBlur}
                    >
                      <option value="" disabled>
                      {langCode === "ar" ? 'اختر نوع العضوية' : 'Choose Membership Type *'}
                      </option>
                      <option value="Individual">Individual</option>
                      <option value="Company">Company</option>
                    </select>
                    <span
                      className={`${styles.icon} ${
                        isOpen ? styles.rotate : ""
                      }`}
                    >
                      <Image
                        src="/images/drop_arrow.png"
                        width={10}
                        height={10}  loading='lazy'
                      />
                    </span>
                  </div>
                </li>
                {membershipType === "Company" && (
                  <li>
                    <input
                      type="text"
                    placeholder={langCode === "ar" ? 'اسم الشركة' : "Company Name"}
                      value={company}
                      onChange={(e) => setCompany(e.target.value)}
                    />
                  </li>
                )}
                <li>
                  <input
                    type="email"
                    placeholder={langCode === "ar" ? 'عنوان البريد الإلكتروني' : "Email Address *"}
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    required
                  />
                </li>
                <li>
                  <div
                    className={
                      langCode === "en"
                        ? styles.password_container
                        : styles.password_container_ar
                    }
                  >
                    <input
                      type={showPassword ? "text" : "password"}
                      placeholder={langCode === "ar" ? 'كلمة المرور' : "Password *" }
                      value={password}
                      onChange={(e) => {
                        setPasswordError("");
                        setPassword(e.target.value);
                      }}
                      required
                      onBlur={handlePasswordChange}
                    />
                    <span onClick={togglePasswordVisibility}>
                      <Image
                        src={
                          showPassword
                            ? "/images/eye_close_icon.png"
                            : "/images/eye_icon.png"
                        }
                        alt="icon"
                        width={22}
                        height={15} loading='lazy'
                      />
                    </span>
                  </div>

                  {passwordError && (
                    <div
                      style={{
                        color: "red",
                        marginTop: "8px",
                        fontSize: "14px",
                      }}
                    >
                      {passwordError}
                    </div>
                  )}
                </li>
                {/* <div style={{marginTop:'15px', display:'none'}}>
                                            <label style={{display:'flex', gap:'15px', alignItems:'center', color:'#787878'}}>
                                                <input type="checkbox" name="show" onChange={togglePasswordVisibility}
                                                style={{background:'transparent',border:'1px solid black', width:'15px'}}
                                                />
                                                Show Password
                                            </label>
                                        </div> */}

                <li>
                  <button
                    type="submit"
                    className={`${styles.btn_style_wrap} btn_style_wrap`}
                  >
                    {isSubmitting ? (
                      <span
                        className={`${styles.btn_style_primary} btn_style_primary`}
                      >
                        ...
                      </span>
                    ) : (
                      <span
                        className={`${styles.btn_style_primary} btn_style_primary`}
                      >
                        {langCode==="ar" ? "إنشاء حساب" : "CREATE ACCOUNT" }
                      </span>
                    )}

                    <span
                      className={`${
                        langCode === "ar" ? "btn_style_arrow_ar" : ""
                      } ${"btn_style_arrow"}`}
                    >
                      <Image
                        src={
                          langCode === "ar"
                            ? "/images/white_arrow_left.png"
                            : "/images/white_right_arw.svg"
                        }
                        alt="icon"
                        width={22}
                        height={15}
                        priority
                      />
                    </span>
                  </button>
                </li>
              </ul>
            </form>
            {message && (
              <p
                className="success-message"
                style={{
                  color: "green",
                  textAlign: "center",
                  marginTop: "10px",
                }}
              >
                {message}
              </p>
            )}
            {error && (
              <p
                className="error-message"
                style={{
                  color: "red",
                  textAlign: "center",
                  marginTop: "10px",
                }}
              >
                {error}
              </p>
            )}
            <span className={styles.dont_have_account}>
             {langCode==="ar" ? "هل لديك حساب بالفعل؟" : "Already have an account?"} <Link href="/login">{langCode==="ar" ? "تسجيل الدخول" : "Login" }</Link>
            </span>

            <style jsx>{`
              .membership-select {
                width: 100%;
                height: 50px;
                padding: 0 20px;
                background: #ffffff;
                border: none;
                letter-spacing: 0.5px;
                font-weight: 400;
                color: #111111;
                font-family: "precioussanstwo-medium";
                border-radius: 9px;
              }
            `}</style>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Index;
