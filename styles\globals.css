@font-face {
  font-family: "precioussanstwo-black";
  src: url("../public/fonts/precioussanstwo-black-webfont.woff2") format("woff2"),
    url("../public/fonts/precioussanstwo-black-webfont.woff") format("woff");
  font-weight: normal;
  font-style: normal;
  font-display: swap;

}

@font-face {
  font-family: "precioussanstwo-medium";
  src: url("../public/fonts/precioussanstwo-medium-webfont.woff2") format("woff2"),
    url("../public/fonts/precioussanstwo-medium-webfont.woff") format("woff");
  font-weight: normal;
  font-style: normal;
  font-display: swap;

}

@font-face {
  font-family: "precioussanstwo-bold";
  src: url("../public/fonts/precioussanstwo-bold-webfont.woff2") format("woff2"),
    url("../public/fonts/precioussanstwo-bold-webfont.woff") format("woff");
  font-weight: normal;
  font-style: normal;
  font-display: swap;

}

@font-face {
  font-family: "precioussanstwo-light";
  src: url("../public/fonts/precioussanstwo-light-webfont.woff2") format("woff2"),
    url("../public/fonts/precioussanstwo-light-webfont.woff") format("woff");
  font-weight: normal;
  font-style: normal;
  font-display: swap;

}

@font-face {
  font-family: "precioussanstwo-thin";
  src: url("../public/fonts/precioussanstwo-thin-webfont.woff2") format("woff2"),
    url("../public/fonts/precioussanstwo-thin-webfont.woff") format("woff");
  font-weight: normal;
  font-style: normal;
  font-display: swap;

}

:root {
  --color-light: #f6f6f6;
  --color-primary: #f16649;
  --color-secondary: #001233;
  --color-gradient: linear-gradient(113.49deg,
      rgba(232, 89, 59, 1) 0%,
      rgba(0, 40, 85, 1) 100%);

  --font-primary: "precioussanstwo-medium";
  --time: 0.35s;

  --color-gradient-ar: linear-gradient(113.49deg,
      rgba(0, 40, 85, 1) 0%,
      rgba(232, 89, 59, 1) 100%);
}

.rtl a[href^="tel:"] {
  unicode-bidi: plaintext;
}

*,
*::before,
*::after {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  /* scroll-behavior: smooth; */
  scroll-padding-top: 200px;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  text-rendering: optimizeLegibility;
}

html,
body {
  max-width: 100vw;
  color: #000;
  font-size: 16px;
  font-weight: 400;
  background: #ffffff;
  font-family: var(--font-primary);
  overflow-x: hidden;
}

body.rtl {
  font-family: var(--font-arabic);
}

body {
  --gap-18: 18px;
  --gap-24: 24px;
  --gap-30: 30px;
  --gap-60: 60px;
  --radius-9: 9px;
  --radius-30: 30px;
  --radius-34: 34px;
  --radius-round: 100px;
  --btn-height: 48px;
  --container-width: 1360px;
}

.body-login {
  background-color: var(--color-secondary);
  min-height: 100dvh;
}

strong {
  font-weight: 600;
  color: inherit;
}

a {
  color: inherit;
  -moz-transition: all var(--time) ease;
  -o-transition: all var(--time) ease;
  -webkit-transition: all var(--time) ease;
  -ms-transition: all var(--time) ease;
  transition: all var(--time) ease;

}

a,
a:hover {
  text-decoration: none;
}

p,
h1,
h2,
h3,
h4,
h5,
h6 {
  margin: 0;
  font-family: var(--font-primary);
}

p {
  line-height: 24px;
}

p:only-child,
p:last-child {
  margin-bottom: 0;
}

ul,
ol {
  padding: 0;
  margin: 0;
  list-style: none;
}

img {
  height: auto;
  max-width: 100%;
}

img,
svg,
video {
  vertical-align: middle;
}

input {

  font-size: 1rem;
}

select,
button,
textarea {
  font-size: 1rem;
  cursor: pointer;
}

select {
  -moz-appearance: none;
  -webkit-appearance: none;
  appearance: none;
}

input::-webkit-autofill-button {
  display: none;
}

input {
  appearance: none;
  /* Hides native browser styles */
  outline: none;
  /* Removes focus outline */
}

textarea {
  resize: none;
  height: 150px;
}

.input_field {
  height: 60px;
  padding: 0 30px;
  border-radius: 100px;
  font-size: 1rem;
}

input[type="email"] {
  text-transform: initial;
}

input[type="search"]::-webkit-search-cancel-button {
  -webkit-appearance: none;
  appearance: none;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

input[type="number"] {
  -moz-appearance: textfield;
  appearance: textfield;
}

.input_field,
.textarea_field {
  width: 100%;
  font-family: var(--font-primary);
  border: 1px solid rgba(128, 128, 128, 0.25);
}

.rtl .input_field,
.rtl .textarea_field {
  font-family: var(--font-arabic);
}

.textarea_field {
  height: 160px;
  padding: 25px 0 0 30px;
  border-radius: 10px;
}

:focus {
  outline: none;
}

.hover-primary:hover {
  color: var(--color-primary);
}

.w-100 {
  width: 100%;
}

.relative {
  position: relative;
}

.overflow-hidden {
  overflow: hidden;
}

.container {
  width: 100%;
  margin: 0 auto;
  max-width: var(--container-width);
  display: block;
}

table {
  width: 100%;
}

/* -------------------------------------------------------------- */

::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  background-color: transparent;
  border-radius: 10px;
}

::-webkit-scrollbar {
  width: 6px;
  height: 3px;
  background-color: transparent;
}

::-webkit-scrollbar-thumb {
  background-color: var(--color-primary);
  border-radius: 30px;
}

/* -------------------------------------------------------------- */

.social_icons {
  display: flex;
  align-items: center;
  justify-content: space-between;
  column-gap: 17px;
  width: fit-content;
}

.social_icons img {
  -moz-transition: all var(--time) ease;
  -o-transition: all var(--time) ease;
  -webkit-transition: all var(--time) ease;
  -ms-transition: all var(--time) ease;
  transition: all var(--time) ease;

}

.social_icons img:hover {
  filter: invert(62%) sepia(39%) saturate(5545%) hue-rotate(336deg) brightness(113%) contrast(72%);
}

/* -------------------------------------------------------------- */

.btn_style_wrap {
  display: flex;
  align-items: center;
  width: fit-content;
  background: transparent;
  border: transparent;
  position: relative;
}

.btn_style_primary {
  font-size: 12.91px;
  font-weight: 500;
  height: var(--btn-height);
  text-align: center;
  padding: 0 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: fit-content;
  color: #ffffff;
  background: var(--color-primary);
  border-radius: 9px;
  letter-spacing: 0.4px;
}

.rtl .btn_style_primary {
  font-family: var(--font-arabic);
}

.btn_style_primary_ar {
  font-size: 12.91px;
  font-weight: 500;
  height: var(--btn-height);
  text-align: center;
  padding: 0 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: fit-content;
  color: #ffffff;
  background: var(--color-primary);
  font-family: var(--font-arabic);
  border-radius: 9px;
  letter-spacing: 0.4px;
}

.btn_style_wrap:hover .btn_style_arrow {
  transform: translateX(5px);
  background: var(--color-primary);
}

.btn_style_arrow {
  width: var(--btn-height);
  height: var(--btn-height);
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--color-primary);
  transform: translateX(-14px);
  transition: transform 0.4s ease-in-out;
  border-radius: 9px;
}

body.rtl .btn_style_arrow {

  transform: translateX(14px);

}

body.rtl .btn_style_wrap:hover .btn_style_arrow {

  transform: translateX(-5px);

}

/* .btn_style_wrap:hover .btn_style_arrow_ar {
  transform: translateX(-5px);
} */

/* .btn_style_arrow_ar {
  transform: translateX(14px);
} */

.inner_drop {
  background: #fff;
  width: 175px;
  border-radius: 10px;
  padding: 10px;
  position: absolute;
  top: 53px;
  left: 0;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.5s ease-in-out, visibility 0s linear 3s;
  flex-direction: column;
}

.inner_drop>li {
  cursor: pointer;
  color: #000;
  width: 100%;
  padding: 5px 0;
  font-size: 16px;
  font-weight: 500;
}

.inner_drop>li:hover {
  color: #d75135;
}

.btn_style_wrap:hover .inner_drop {
  opacity: 1;
  visibility: visible;
  transition: opacity 0.3s ease-in-out, visibility 0s linear 0s;
}

/* -------------------------------------------------------------- */

.main_title,
.main_title_center {
  font-size: 2.8125rem;
  font-weight: 600;
  line-height: 61px;
  letter-spacing: -1px;
  /* font-family: "precioussanstwo-medium"; */
}

.main_title_center {
  text-align: center;
}

.title p+p {
  margin-top: 20px;
}

/* -------------------------------------------------------------- */

.zoom-effect {
  overflow: hidden;
}

.zoom-effect>a {
  display: block;
}

.zoom-effect img {
  position: relative;
  -webkit-transform: scale(1);
  transform: scale(1);
  -webkit-transition: all var(--time) ease-in-out;
  transition: all var(--time) ease-in-out;
}

.zoom-effect:hover img {
  -webkit-transform: scale(1.12);
  transform: scale(1.12);
}

/* -------------------------------------------------------------- */

.home-news .swiper-button-next,
.home-news .swiper-button-prev {
  position: absolute;
  top: -102px;
  width: 48px;
  height: 48px;
  background: var(--color-primary);
  transition: background 0.4s ease-in-out;
  cursor: pointer;
  border-radius: 9px;
}

.home-news .swiper-button-next:hover,
.home-news .swiper-button-prev:hover {
  background-color: #d75135;
}

.home-news .swiper-button-next {
  right: 56px;
  background-image: url("../public/images/white_left_arw.svg");
  background-repeat: no-repeat;
  background-position: center center;
}

.home-news .swiper-button-prev {
  right: 0;
  background-image: url("../public/images/white_right_arw.svg");
  background-repeat: no-repeat;
  background-position: center center;
}

.rtl .home-news .swiper-button-prev {
  left: 56px;
  right: unset;
}

.rtl .home-news .swiper-button-next {
  left: 0px;
  right: unset;
}

/* -------------------------------------------------------------- */

/* -------------------------------------------------------------- */

.marquee_slider .swiper-slide {
  width: fit-content;
}

.banner_marquee .swiper {
  overflow: visible;
}

.home-news {
  /* background-image: url(../public/images/news_grdnt.png); */
  background-image: url(../public/images/news_grdnt.webp);
  background-repeat: no-repeat;
  background-position: top center;
}

.home-news .swiper,
.home-news .swiper-wrapper {
  position: initial;
}

.home-news .container {
  position: relative;
}

.home-news .swiper {
  overflow: visible;
}

.historyslider .swiper-slide:not(.swiper-slide-active) {
  opacity: 0;
  visibility: hidden;
}

/* -------------------------------------------------------------- */

.react-tabs__tab--selected {
  background: transparent !important;
}

/* -------------------------------------------------------------- */

.body-sukuk .sixcolumn_item {
  align-items: center;
}

/* -------------------------------------------------------------- */

.center_bg {
  /* background-image: url(/images/explore_grdnt.png); */
  background-image: url(/images/explore_grdnt.webp);
  background-repeat: no-repeat;
  background-position: center center;
}

.center_bg_2 {
  /* background-image: url(/images/find_rating_grd.png); */
  background-image: url(/images/find_rating_grd.webp);
  background-repeat: no-repeat;
  background-position: center -500px;
}

/* -------------------------------------------------------------- */

.rating-sevices {
  margin-top: 150px;
}

/* -------------------------------------------------------------- */

.overview_title p {
  color: #d7d7d7;
  line-height: 26px;
}

.overview_title p+p {
  margin-top: 30px;
}

/* -------------------------------------------------------------- */

.ReactModal__Overlay {
  background: rgba(0, 0, 0, 0.7) !important;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 11;
}

.ReactModal__Content {
  max-width: 800px;
  width: calc(100% - 6%);
  background: #ffffff;
  /* min-height: 400px; */
  margin: auto;
  position: relative;
  overflow: hidden;
  border-radius: 10px;
}

.ReactModal__Content video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.closeButton {
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  top: 10px;
  right: 10px;
  background: transparent;
  font-size: 0.875rem;
  cursor: pointer;
  width: 20px;
  height: 20px;
  line-height: 1;
  background: #ffffff;
  border-radius: 50%;
  z-index: 3;
}

.closeButton.closeButton_new {
  color: white;
  width: 25px;
  height: 25px;
}

.closeButton.closeButton_new::after,
.closeButton.closeButton_new::before {
  content: " ";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(45deg);
  height: 1.5px;
  border-radius: 10px;
  width: 55%;
  background: rgb(0, 0, 0);
}

.closeButton.closeButton_new::before {

  transform: translate(-50%, -50%) rotate(135deg);

}

/* ------------------------------------------------------
    ebin
------------------------------------------------------ */

.banner_text {
  font-size: 3.25rem;
  line-height: 55px;
  /* font-family: "precioussanstwo-medium"; */
  font-weight: 500;
}

.service_title {
  width: 100%;
  max-width: 965px;
  padding-bottom: 40px;
}

.service_title p {
  color: #d7d7d7;
  margin-bottom: 15px;
}

.service_title .main_title {
  margin-bottom: 15px;
}

.overview_title {
  width: 100%;
  max-width: 1150px;
  padding-bottom: 40px;
}

.overview_title:last-child {
  padding: 0;
}

.overview_title p {
  color: #d7d7d7;
  margin-bottom: 15px;
}

.overview_title p:last-child {
  margin-bottom: 0;
}

.overview_title .main_title {
  line-height: 1;
  margin-bottom: 25px;
}

.bg_left {
  width: 100%;
  position: relative;
}

.bg_left .relative {
  position: relative;
  z-index: 2;
}

.bg_main {
  width: 100%;
  position: relative;
  z-index: 2;
}

.bg_main::after {
  content: "";
  width: 100%;
  height: 100%;
  /* background-image: url(../public/images/bg-center.png); */
  background-image: url(../public/images/bg-center.webp);
  position: absolute;
  background-repeat: no-repeat;
  background-position: top center;
  top: 0;
  z-index: 0;
}

.bg_left::after {
  content: "";
  width: 100%;
  height: 100%;
  background-image: url(../public/images/left-bg.png);
  position: absolute;
  background-repeat: no-repeat;
  background-position: top left;
  top: 0;
  z-index: 0;
  opacity: 0.8;
}

.overview_with_image {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
}

.overview_with_image .overview_title {
  width: 65%;
  padding-right: 5%;
}

.overview_img {
  width: 35%;
}

.overview_img img {
  width: 100%;
  -webkit-border-radius: 20px;
  -moz-border-radius: 20px;
  border-radius: 20px;
}

.read_btn {
  color: #fff;
  margin-top: 35px;
  font-weight: 600;
  display: block;
}

.read_btn:hover {
  color: var(--color-primary);
}

.bg_main .relative {
  position: relative;
  z-index: 4;
}

.margin-center {
  margin: 0 auto;
}

.flex {
  display: flex;
  flex-wrap: wrap;
}

.table_sorting_title .reports_select {
  width: 230px;
}

.year_select {
  width: 140px;
}

.table_sorting_title .select_wrap .selected {
  height: 48px;
  line-height: 48px;
  border-radius: 9px;
}

.table_sorting_title .select_wrap .selected {
  background-image: url(../public/images/select_outline_arrow1.svg);
  background-repeat: no-repeat;
  background-position: center right 15px;
}

body.rtl .table_sorting_title .select_wrap .selected {

  background-position: center left 15px !important;
}

.section_second .profile_content h3 {
  color: #0b1f51;
}

.section_second .profile_content p {
  color: #021333;
}

.sixcolumn_item:hover .btn_style_wrap:not(:hover) .btn_style_arrow,
.sixcolumn_item:hover .btn_style_wrap:not(:hover) .btn_style_primary {
  background: transparent;
  border: 1px solid var(--color-primary);
}

.sixcolumn_item:hover .btn_style_wrap:not(:hover) .btn_style_primary {
  border-right: none;
  border-radius: 9px 0 0 9px;
}

.sixcolumn_item:hover .btn_style_wrap:not(:hover) .btn_style_arrow {
  border-left: none;
  border-radius: 0 9px 9px 0;
}

.sixcolumn_item:hover .btn_style_wrap:not(:hover) .btn_style_arrow {
  transform: translateX(0);
}

/* =======ar======= */

.sixcolumn_item_ar:hover .btn_style_wrap_ar:not(:hover) .btn_style_arrow_ar,
.sixcolumn_item_ar:hover .btn_style_wrap_ar:not(:hover) .btn_style_primary_ar {
  background: transparent;
  border: 1px solid var(--color-primary);
}

.sixcolumn_item_ar:hover .btn_style_wrap_ar:not(:hover) .btn_style_primary_ar {
  border-left: none;
  border-radius: 0 9px 9px 0;
}

.sixcolumn_item_ar:hover .btn_style_wrap_ar:not(:hover) .btn_style_arrow_ar {
  border-right: none;
  border-radius: 9px 0 0 9px;
}

.sixcolumn_item_ar:hover .btn_style_wrap_ar:not(:hover) .btn_style_arrow_ar {
  transform: translateX(0);
}

/*---------arabic style-------*/
.rtl .news_top_right_ar {
  padding-left: 0px;
  padding-right: 20px;
}

.rtl .news_left_content_ar {
  padding-left: 0px;
  padding-right: 15px;
}

.rtl .news_left_content_ar label,
.rtl .news_main_title_ar label {
  padding-left: 0px;
  padding-right: 34px;
}

.rtl .news_left_content_ar label::after,
.rtl .news_main_title_ar label::after {
  left: unset;
  right: 0px;
}

.rtl .news_main_title_ar {
  padding: 25px 25px 25px 150px;
}

.rtl .news_detail_left_ar {
  padding-right: 0px;
  padding-left: 8%;
}

.rtl .inner_drop {
  right: 0;
}

input::placeholder {
  color: #595959;
}

.btn_style_wrap .drop_btn_icon>img {
  transition: transform 0.5s ease;
}

.btn_style_wrap:hover .drop_btn_icon>img {
  transform: rotate(180deg);
}

.sixcolumn_inside.text_size {
  max-width: 100% !important;
}

.capc_icon {
  display: inline-block;
  margin-left: 10px;
  margin-right: 10px;
  width: 25px;
  height: auto;
}

.capc_icon img {
  height: auto;
  width: 100%;
  display: block;
  object-fit: contain;
}

@media (max-width:700px) {
  .capc_icon {

    width: 20px;

  }

}

@media (max-width: 1600px) {
  body {
    --container-width: 1160px;
  }
}

@media (max-width: 1200px) {
  body {
    --gap-30: 20px;
    --radius-30: 20px;
    --radius-34: 24px;
    --btn-height: 44px;
  }

  html,
  body {
    font-size: 14px;
  }

  html {
    scroll-padding-top: 120px;
  }

  .container {
    max-width: 92%;
  }

  .btn_style_primary {
    padding: 0 30px;
  }

  .btn_style_primary_ar {
    padding: 0 30px;
  }

  .main_title,
  .main_title_center {
    font-size: 2rem;
    line-height: 38px;
  }

  .section_third .title {
    padding: 0;
  }
}

@media (max-width: 860px) {
  p {
    line-height: 21px;
  }

  body {
    --gap-18: 14px;
    --gap-24: 16px;
  }

  .btn_style_primary {
    padding: 0 15px;
  }

  .btn_style_primary_ar {
    padding: 0 15px;
  }

  .home-news .swiper-button-next,
  .home-news .swiper-button-prev {
    top: -77px;
    width: 44px;
    height: 44px;
  }

  .overview_title p+p {
    margin-top: 20px;
  }

  .news_detail_right+.pt-50 {
    padding: 0;
  }

  .overview_title .main_title {
    margin-bottom: 20px;
  }
}

@media (max-width: 767px) {

  .main_title,
  .main_title_center {
    font-size: 1.75rem;
    line-height: 35px;
  }

  .rating-sevices .d-flex-center {
    flex-direction: column;
    text-align: center;
    row-gap: 20px;
  }

  .year_select,
  .table_sorting_title .reports_select {
    width: calc(100% - 100px);
  }

  .table_sorting_title .select_wrap .selected {
    height: 44px;
    line-height: 44px;
  }

  .mr-t-20 {
    margin-top: 20px;
  }
}


.body-regulatory-reports .footer_top.aos-init {
  opacity: 1;
  transform: translateZ(0);
}

.ratings_rating_card_body__z0cSY {
  max-width: 500px;
}

/* .grecaptcha-badge {
    display: none !important;
  } */

.pin-panel .card {
  padding: 60px !important;
}

.pin-panel .card {
  background-size: 700px !important;
  background-position: center right;
}

.rtl .pin-panel .card {
  background-position: center left;
}


@media (max-width: 1600px) {

  .pin-panel .card {
    background-size: 550px !important;
    background-position: center right;
  }

  .rtl .pin-panel .card {
    background-position: center left;
  }

}

@media (max-width: 1200px) {
  .pin-panel .card {
    /* background-size: 430px !important; */
    background-size: 350px !important;
    background-position: center right;
  }

  .rtl .pin-panel .card {
    background-position: center left;
  }
}

@media (max-width: 767px) {
  body {
    --gap-30: 15px;

  }

  .pin-panel .card {
    padding: 30px !important;
  }
}