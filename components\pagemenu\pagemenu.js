import React, { useEffect, useState } from "react";
import Link from "next/link";
import { useRouter } from "next/router";
import styles from "./pagemenu.module.css";

const Pagemenu = ({ menuItems, isTab = false }) => {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState("");

  // Synchronize the active tab with the current route on initial render and route changes
  useEffect(() => {
    const currentPath = router.asPath.split("#").join("/#");
    setActiveTab(currentPath);
  }, [router.asPath]);

  // Handle tab switching manually
  const handleTabClick = (url) => {
    setActiveTab(url); // Update active tab state
    !isTab && router.push(url); // Navigate to the corresponding route
  };
  // console.log("menus", menuItems);
  return (
    <div className={styles.pagemenu}>
      <ul className={styles.pagemenu_list}>
        {menuItems.map((item, index) => (
          <li key={index}>
            <Link
              href={item.tab_links.url}
              className={`${styles.pagemenu_link} ${
                (!isTab && activeTab === item.tab_links.url) ||
                (isTab && activeTab === item.tab_links.title)
                  ? styles.pagemenu_link_active
                  : ""
              }`}
              onClick={(e, index) => {
                e.preventDefault(); // Prevent default anchor behavior
                handleTabClick(
                  !isTab ? item.tab_links.url : item.tab_links.title
                ); // Handle tab switching
              }}
            >
              {item.tab_links.title}
            </Link>
          </li>
        ))}
      </ul>
    </div>
  );
};

export default Pagemenu;
