import React, { useState } from "react";
import Link from "next/link";
import Image from "next/image";
import { useRouter } from "next/router";
import styles from "@/styles/login.module.css";

const Index = () => {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [message, setMessage] = useState("");
  const [error, setError] = useState("");
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const { locale } = useRouter();
  const langCode = locale === "ar" ? "ar" : "en";

  //  redirect URL
  const { redirect } = router.query;

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    setMessage("");
    setError("");

    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_LOGIN_API_URL}`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ username: email, password }),
      });

      const data = await response.json();
      setIsSubmitting(false);

      if (response.ok) {
        // Save the token and user data in local storage or state
        localStorage.setItem("token", data.token);
        const userDetails = await fetchUserDetails(data.token);
        localStorage.setItem("user", JSON.stringify(userDetails.id));
        // localStorage.setItem('membershipType', userDetails.meta.membership_type);
        // Fetch usermeta data (if required)
        const userMeta = await fetchUserMeta(data.token);
        if (userMeta) {
          // Store specific user meta if needed
          localStorage.setItem(
            "membershipType",
            JSON.stringify(userMeta.membership_type[0])
          );
        }

        // setMessage('Login successful! Redirecting to dashboard...');
        if (redirect) {
          setMessage("Login successful!");
        } else {
          setMessage("Login successful! Redirecting to dashboard...");
        }

        setTimeout(() => {
          setMessage("");
        }, 3000);
        // router.push('/dashboard');
        router.push(redirect || "/dashboard");
      } else {
        setError(data.message || "Login failed. Please try again.");
        setTimeout(() => {
          setError("");
        }, 4000);
      }
    } catch (err) {
      setError("An unexpected error occurred. Please try again.");
      console.error("Login error:", err);
      setTimeout(() => {
        setError("");
      }, 3000);
    }
  };

  const fetchUserDetails = async (token) => {
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/users/me`,
        {
          method: "GET",
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      const data = await response.json();
      //console.log(data)
      return response.ok ? data : null;
    } catch (err) {
      console.error("Error fetching user details:", err);
      return null;
    }
  };

  const fetchUserMeta = async (token) => {
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/usermeta`,
        {
          method: "GET",
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      const data = await response.json();
      console.log(data);
      return response.ok ? data : null;
    } catch (err) {
      console.error("Error fetching usermeta:", err);
      return null;
    }
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  return (
    <section className={`${styles.login_page} bg-secondary`}>
      <div className={`${styles.container} container`}>
        <div className={styles.login_wrap}>
          <div className={`${styles.login_title} text-center text-white`}>
            <h1 className="main_title mb-20">{langCode === "ar" ? "تسجيل الدخول" : "Login" }</h1>
            <p className="color-pera">{langCode === "ar" ? "الرجاء إدخال بريدك الإلكتروني وكلمة المرور" : "Please enter your e-mail and password"}</p>
          </div>
          <div className={styles.login_body}>
            <form onSubmit={handleSubmit}>
              <ul>
                <li>
                  <input
                    type="text"
                    placeholder={langCode === "ar" ? "عنوان البريد الإلكتروني *" : "Email Address *" }
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    required
                  />
                </li>
                <li>
                  <div
                    className={
                      langCode === "en"
                        ? styles.password_container
                        : styles.password_container_ar
                    }
                  >
                    <input
                      type={showPassword ? "text" : "password"}
                      placeholder={langCode === "ar" ? "كلمة المرور *" : "Password *" }
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      required
                    />
                    <span onClick={togglePasswordVisibility}>
                      <Image
                        src={
                          showPassword
                            ? "/images/eye_close_icon.png"
                            : "/images/eye_icon.png"
                        }
                        alt="icon"
                        width={22}
                        height={15} loading='lazy'
                      />
                    </span>
                  </div>
                </li>
                <li>
                  <button
                    className={`${styles.btn_style_wrap} btn_style_wrap`}
                    type="submit"
                  >
                    {isSubmitting ? (
                      <span
                        className={`${styles.btn_style_primary} btn_style_primary`}
                      >
                        ...
                      </span>
                    ) : (
                      <span
                        className={`${styles.btn_style_primary} btn_style_primary`}
                      >
                        {langCode === "ar" ? "إرسال" : "Submit"}
                      </span>
                    )}
                    <span
                      className={`${
                        langCode === "ar" ? "btn_style_arrow_ar" : ""
                      } ${"btn_style_arrow"}`}
                    >
                      <Image
                        src={
                          langCode === "ar"
                            ? "/images/white_arrow_left.png"
                            : "/images/white_right_arw.svg"
                        }
                        alt="icon"
                        width={22}
                        height={15}
                        priority
                      />
                    </span>
                  </button>
                </li>
              </ul>
            </form>
            {message && (
              <p
                className="success-message"
                style={{
                  color: "green",
                  textAlign: "center",
                  marginTop: "10px",
                }}
                dangerouslySetInnerHTML={{ __html: message }}
              />
            )}
            {error && (
              <p
                className="error-message"
                style={{
                  color: "red",
                  textAlign: "center",
                  marginTop: "10px",
                }}
                dangerouslySetInnerHTML={{ __html: error }}
              />
            )}
            <span className={styles.dont_have_account}>
              {langCode === "ar" ? "ليس لديك حساب؟" : "Don't have an account?" } 
              <Link href="/register">{langCode === "ar" ? "إنشاء حساب" : "Create One" }</Link>
            </span>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Index;
