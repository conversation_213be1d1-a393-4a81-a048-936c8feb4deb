.information {
  display: flex;
  flex-flow: column wrap;
  row-gap: 120px;
}

.information h4 {
  color: #ffffff;
  font-size: 1.5625rem;
  line-height: 1;
  font-weight: 400;
  margin-bottom: 30px;
}

.form_style {
  display: flex;
  flex-wrap: wrap;
  row-gap: 17px;
  column-gap: 20px;
}

.form_style li {
  width: calc(50% - 10px);
}

.form_style input {
  padding: 0 20px;
  height: 50px;
  border: none;
  width: 100%;
  background: #ffffff;
  border-radius: 9px;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

:global(body.rtl) .form_style input {
  font-family: var(--font-arabic);
}

.btn_style_wrap {
  margin-top: 35px;
}

.form_style input::-webkit-inner-spin-button,
.form_style input::-webkit-calendar-picker-indicator {
  display: none;
  -webkit-appearance: none;
}

.form_style input::-webkit-contacts-auto-fill-button {
  visibility: hidden;
  display: none !important;
  pointer-events: none;
  position: absolute;
  right: 0;
}

@media (max-width: 1200px) {
  .information {
    row-gap: 80px;
  }
}

@media (max-width: 860px) {
  .information {
    row-gap: 50px;
  }
  .information h4 {
    margin-bottom: 20px;
  }
}
@media (max-width: 767px) {
  .form_style input {
    height: 44px;
  }
}
@media (max-width: 600px) {
  .form_style li {
    width: 100%;
  }
}
