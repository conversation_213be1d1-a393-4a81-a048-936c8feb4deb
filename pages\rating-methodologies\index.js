import React from "react";
import styles from "./ratingMethodologies.module.css";
import Pagemenu from "@/components/pagemenu/pagemenu";
import SubBanner from "@/components/innerbanner/innerbanner";
import Tablestyle from "@/components/tablestyle/tablestyle";
import Tabstyle from "@/components/tabstyle/tabstyle";
import { fetchPageBySlug } from "@/lib/api/PageBySlug";
import {fetchRatingTab} from "@/lib/api/byfield"
import Head from "next/head";


const Index = ({pageData, Tabs}) => {
	const banner = pageData.acf.banner_details || '';
	const data = pageData.acf.rating_methodologies || '';
	const taabs = pageData.acf.details_process || '';
	return (
		<>
			
			<SubBanner
				bannerDetails={banner}
				alignEnd={false}
			/>
			<Pagemenu menuItems={Tabs.tabs} />
			<section className="pt-120 bg-secondary center_bg">
				<div className="table_block" data-aos="fade-up">
					<div className="container">
						<div className="text-white mb-50">
							<h2 className="main_title mb-20" 
								dangerouslySetInnerHTML={{ __html: data.title }}	
							/>
							<p className="color-pera"
								dangerouslySetInnerHTML={{ __html: data.description }}	
							/>
						</div>
						<Tablestyle
							documents={data.list}
							className={styles.table_two_column}
						/>
					</div>
				</div>
				{/* <div className={styles.tab_block} data-aos="fade-up">
					<div className="container">
						<div className="text-white">
							<h2 className="main_title text-center mb-30" dangerouslySetInnerHTML={{ __html: taabs.title }} />
						</div>
						<Tabstyle tabList={taabs} />
					</div>
				</div> */}
			</section>
		</>
	);
};

export default Index;

export async function getServerSideProps({locale}) {
	const langCode = locale === "ar" ? "ar" : "en";
	try {
		const pageData = await fetchPageBySlug("rating-methodologies", langCode);
		const Tabs = await fetchRatingTab(langCode);
		return {
			props: {
				pageData,
				Tabs
			},
		};
	} catch (error) {
		console.error("Failed to fetch data:", error);
		return {
			props: {
				pageData: null
			},
		};
	}
}