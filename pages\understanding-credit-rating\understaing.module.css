.btn_style_wrap {
    margin: 40px auto 0 auto;
    justify-content: center;
}

.video_block {
    position: relative;
}

.video_block>img {
    border-radius: 1000px;
}

.play_btn {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 110px;
    height: 110px;
    display: flex;
    cursor: pointer;
    align-items: center;
    justify-content: center;
    background: #ffffff;
    border-radius: 50%;
    z-index: 1;
}

.play_btn:hover {
    background: var(--color-primary);
}

.play_btn:hover img {
    filter: invert(51%) sepia(26%) saturate(7%) hue-rotate(148deg) brightness(167%) contrast(102%);
}

.sixcolumn {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--gap-24);
    margin-top: 120px;
}


@media (max-width: 1200px) {
    .play_btn {
        width: 80px;
        height: 80px;
    }

    .sixcolumn {
        margin-top: 80px;
    }
}

@media (max-width: 860px) {
    .sixcolumn {
        margin-top: 50px;
        grid-template-columns: repeat(2, 1fr);
    }

    .btn_style_wrap {
        margin:25px auto 0 auto;
    }
}

@media (max-width: 767px) {
    .video_block>img {
        border-radius: 20px;
        height:250px;
        object-fit: cover;
    }
}
@media (max-width: 600px) {
    .sixcolumn {
        grid-template-columns: repeat(1, 1fr);
    }
}