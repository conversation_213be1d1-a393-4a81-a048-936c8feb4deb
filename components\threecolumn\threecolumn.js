import React from "react";
import Image from "next/image";
import styles from "./threecolumn.module.css";

const Threecolumn = ({ columns }) => {
	return (
		<div className={`${styles.threecolumn_list} d-flex-row`}>
			{columns.map((column, index) => (
				<div
					key={index}
					className={`${styles.threecolumn_item} d-flex-column d-flex-ycenter bg-white`}
					data-aos="fade-up"
				>
					<div
						className={`${styles.threecolumn_circle} d-flex-center bg-primary threecolumn_circle`}
					>
						<Image loading='lazy' src={column.icon} alt={column.alt} width={60} height={60} />
					</div>
					<div className="threecolumn_content text-center">
						<h3 dangerouslySetInnerHTML={{ __html: column.title }}  />
						<p dangerouslySetInnerHTML={{ __html: column.description }}  />
					</div>
				</div>
			))}
		</div>
	);
};

export default Threecolumn;
