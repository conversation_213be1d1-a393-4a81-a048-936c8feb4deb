
import React from "react";
import Image from "next/image";
import styles from "./profile.module.css";

const Profile = ({ profiles }) => {
    return (
        <>
            {profiles.map((team, index) => (
                <div key={index} className={styles.profile_wrap} data-aos="fade-up">
                    <div className={`${styles.profile_image} zoom-effect`}>
                        <Image
                            src={team.photo}
                            alt={team.name || "Profile Image"}
                            width={325}
                            height={360} loading='lazy'
                        />
                    </div>

                    <div className={`${styles.profile_content} profile_content`}>
                        <h3>{team.name}</h3>
                        <p>{team.designation}</p>
                    </div>
                </div>
            ))}
        </>
    );
};

export default Profile;