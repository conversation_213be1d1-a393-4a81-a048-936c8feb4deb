.customSelect {
    position: relative;
    display: inline-block;
    width: 100%;
}

.label {
    cursor: pointer;
    margin-bottom: 5px;
    font-weight: bold;
}

.selected {
    width: 100%;
    height: 54px;
    line-height: 50px;
    background: transparent;
    border: 2px solid #fffffff7;
    color: #fff;
    opacity: 0.55;
    padding: 0 50px 0 20px;
    border-radius: 19px;
    background-image: url(../../public/images/select_arw.svg);
    background-repeat: no-repeat;
    background-position: 97% 50%;
    cursor: pointer;
}

:global(body.rtl) .selected {
    background-position: right 97% top 50% !important;
    padding: 0 20px 0 50px;
}

.dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    z-index: 10;
    background-color: #fff;
    border-radius: 4px;
    margin-top: 5px;
    /* max-height: 200px; */
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    padding: 25px 30px;
    border-radius: 20px;

    &::-webkit-scrollbar-track {
        margin-top: 20px;
        margin-bottom: 20px;


    }
}

.dropdownItem {
    cursor: pointer;
    font-size: 0.875rem;
}

.dropdownItem+.dropdownItem {
    margin-top: 20px;
}

.dropdownItem:hover {
    color: var(--color-primary);
}