.first_section {
    display: flex;
    flex-flow: column wrap;
    row-gap: 80px;
}


.section_second {
    padding: 90px 0 70px 0;
}

.profile_list {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: var(--gap-24);
}



@media (max-width: 1200px) {
    .profile_list {
        grid-template-columns: repeat(3, 1fr);
    }

    .section_second {
        padding: 80px 0;
    }
    .first_section {
        row-gap:40px;
    }
}

@media (max-width: 860px) {
    .section_second {
        padding: 50px 0;
    }

    .profile_list {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 600px) {
    .profile_list {
        grid-template-columns: repeat(1, 1fr);
    }
}