.first_content {
    max-width: 1045px;
    margin: auto;
}

.first_form_list {
    display: flex;
    flex-wrap: wrap;
    column-gap: 16px;
    row-gap: 10px;
}

.first_form_list li {
    width: 100%;
}

.first_form_list li:not(:last-child) {
    width: calc(50% - 8px);
}

.first_form_list label {
    display: block;
    color: #fff;
    margin-bottom: 12px;
}

.first_form_list input {
    width: 100%;
    height: 54px;
    background: transparent;
    border: 2px solid #ffffff8f;
    color: #ffffff;
    padding: 0 20px;
    border-radius: 19px;
}

.btn_style_wrap {
    margin: 30px auto 0 auto;
}


/* ----------------------------------------------------------------------- */

.table_style_second table {
    width: 100%;
    border-collapse: collapse;
    background: linear-gradient(180deg,
            rgba(255, 255, 255, 0.12) 0%,
            rgba(0, 84, 135, 0.12) 100%);
    backdrop-filter: blur(12px);
    border-radius: 19px;
}

.table_style_second table th {
    color: #ffffff;
    font-size: 18px;
    padding: 27px 0;
    background: #005487;
    font-weight: 400;
    min-width: 100px;

}

@media (max-width:768px) {
    .table_style_second table th {
        font-size: 16px;
    }

    .table_style_second table tr td {
        font-size: 14px;
    }

}

.table_style_second td:first-child,
.table_style_second th:first-child {
    padding-left: 38px;
    width: 35%;
}

.table_style_second table th:not(:last-child),
.table_style_second table td:not(:last-child) {
    padding-right: 15px;

}

:global(body.rtl) .table_style_second table th:not(:first-child),
:global(body.rtl) .table_style_second table td:not(:first-child) {
    padding-right: unset;
    padding-left: 15px;

}

.table_style_second td:not(:first-child, :nth-child(2), :nth-child(3)) {
    text-align: center;
}

.table_style_second th:nth-child(2) {
    width: 11.5%;
}

.table_style_second th:nth-child(4) {
    width: 12%;
}

.table_style_second th:nth-child(5) {
    width: 12%;
}

.table_style_second thead th:first-child {
    border-radius: 20px 0 0 0;
}

.table_style_second thead th:last-child {
    border-radius: 0 20px 0 0;
}

.table_style_second table td {
    color: #ffffff;
    font-size: 16px;
    padding: 27px 0;
    font-weight: 400;
    text-align: left;
}

.table_style_second tbody tr {
    padding: 0 20px;
    position: relative;
}

.table_style_second tbody tr::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 20px;
    right: 20px;
    height: 1px;
    background: #6d758f;
}

.table_style_second tbody tr:last-child::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 20px;
    right: 20px;
    height: 1px;
    background: var(--color-secondary) !important;
}

.pdf_link {
    display: flex;
    align-items: center;
    column-gap: 8px;
    font-size: 20px;
    text-transform: uppercase;
}

.table_style_second {
    overflow-y: auto;
}

.main_table_wrap {
    min-width: 978px;
}

:global(body.rtl) .table_style_second thead th:first-child {
    border-radius: 0 20px 0 0;
}

:global(body.rtl) .table_style_second thead th:last-child {
    border-radius: 20px 0 0 0;
}

:global(body.rtl) .table_style_second td:first-child,
:global(body.rtl) .table_style_second th:first-child {
    padding-right: 38px;
    width: 35%;
}

:global(body.rtl) .table_style_second table th {
    text-align: start;
}

:global(body.rtl) .table_style_second table td {
    text-align: right;
}

:global(body.rtl) .table_style_second td:not(:first-child, :nth-child(2), :nth-child(3)) {
    text-align: center;
}

:global(body.rtl) .table_style_second th:not(:first-child, :nth-child(2), :nth-child(3)) {
    text-align: center;
}



@media (max-width: 600px) {

    .first_form_list li,
    .first_form_list li:not(:last-child) {
        width: 100%;
    }
}