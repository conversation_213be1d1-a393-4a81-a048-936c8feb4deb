.my_account .title {
    margin: 80px 0;
}

.container_inside {
    display: flex;
    align-items: flex-start;
    row-gap: 30px;
    column-gap: 45px;
}

.left_navigation {
    width: 22.3%;
    padding: 30px;
    background: #ffffff;
    border-radius: 20px;
}

.left_navigation h4 {
    color: var(--color-secondary);
    font-size: 1.5rem;
    margin-bottom: 20px;
}

/* ----------------------------------------------------------- */

.my_account_right {
    width: calc(78.7% - 45px);
    max-width: 1014px;
    display: flex;
    flex-flow: row wrap;
    gap: 25px;
}

.my_account_right h4{
    color: #ffffff;
    font-size: 1.5625rem;
    line-height: 1;
    font-weight: 600;
    margin: 0;
}

@media (max-width: 1200px) {
    .left_navigation {
        padding: 20px;
        width: 25%;
    }

    .container_inside {
        column-gap: 30px;
    }

}

@media (max-width: 860px) {
    .container_inside {
        flex-wrap: wrap;
    }

    .my_account_right,
    .left_navigation {
        width: 100%;
    }

    .my_account .title {
        margin: 100px 0 30px 0;
    }
}
