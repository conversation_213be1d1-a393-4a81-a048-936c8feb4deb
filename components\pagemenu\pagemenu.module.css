.pagemenu {
  background: #1f2e58;
  padding: 0 3%;
  position: sticky;
  top: 0;
  overflow: auto;
}

.pagemenu_list {
  display: flex;
  column-gap: 50px;
  justify-content: center;
  min-width: 1024px;
}

.pagemenu_link {
  display: block;
  color: #ffffff;
  font-size: 1.125rem;
  position: relative;
  padding: 25px 0;
  white-space: nowrap;
}

.pagemenu_list > li:last-child {
  margin-right: 20px !important;
}

:global(body.rtl) .pagemenu_list > li:last-child {
  margin-left: 20px !important;
}

.pagemenu_link::after {
  content: "";
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  margin: auto;
  background: url(../../public/images/dashicons_arrow-up.svg) no-repeat;
  width: 13px;
  height: 9px;
  visibility: hidden;
  opacity: 0;
}

.pagemenu_link_active::after {
  opacity: 1;
  visibility: visible;
}

.pagemenu_list::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 3px rgba(0, 0, 0, 0.3);
  background-color: transparent;
  border-radius: 10px;
}

.pagemenu_list::-webkit-scrollbar {
  width: 3px;
  height: 3px;
  background-color: transparent;
}

.pagemenu_list::-webkit-scrollbar-thumb {
  background-color: var(--color-primary);
  border-radius: 30px;
}

.pagemenu_link_active {
  color: var(--color-primary);
}

@media (min-width: 861px) {
  .pagemenu_link:hover {
    color: var(--color-primary);
  }
}

@media (max-width: 860px) {
  .pagemenu_link {
    font-size: 1rem;
    padding: 10px 0;
  }

  .pagemenu_list {
    /* flex-wrap: wrap; */
    column-gap: 30px;
  }

  .pagemenu_link::after {
    display: none;
  }

  .pagemenu_link_active {
    color: var(--color-primary);
  }
  .pagemenu_list {
    min-width: auto;
    width: fit-content;
    margin: auto;
    justify-content: center;
  }
  :global(body.rtl) .pagemenu_list {
    min-width: auto;
    width: fit-content;
    margin: auto;
    justify-content: center;
  }
}
