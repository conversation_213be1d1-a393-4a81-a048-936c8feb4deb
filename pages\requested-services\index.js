import React, { useState, useEffect } from "react";
import Link from "next/link";
import Image from "next/image";
import page from "./requestedservices.module.css";
import Sidemenu from "@/components/sidemenu/sidemenu";
import styles from "@/pages/dashboard/myaccount.module.css";
import DasboardLayout from "@/components/DashboardLayout";
import ProtectedRoute from "@/components/ProtectedRoute";
import { fetchPageBySlug, fetchRequest, fetchServices } from "@/lib/api/PageBySlug";
import { useRouter } from "next/router";

const Index = ({ pageData, serviceList }) => {
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 6;
  const [selectedService, setSelectedService] = useState("");
  const [sortByDate, setSortByDate] = useState(""); // New state for date sorting
  const [requestList, setRequestList] = useState([]); // State for request list
  const [userId, setUserId] = useState(null); // State to store userId

  const { locale } = useRouter(); 
    const langCode = locale === 'ar' ? 'ar' : 'en'; 
  useEffect(() => {
    const user = JSON.parse(localStorage.getItem("user"));
    if (user) {
      setUserId(user); 
    }
  }, []); 

  useEffect(() => {
    if (userId) {
      const fetchData = async () => {
        try {
          const data = await fetchRequest("en", "100", userId); 
          setRequestList(data); // Set request list state
        } catch (error) {
          console.error("Failed to fetch request data:", error);
        }
      };

      fetchData();
    }
  }, [userId]); // Re-run this effect when userId changes

  const filteredRequestList = selectedService
    ? requestList.filter((request) => request.acf.type_request === selectedService)
    : requestList;

  // Sort by date before pagination
  const sortedAndFilteredRequestList = [...filteredRequestList].sort((a, b) => {
    const dateA = new Date(a.date);
    const dateB = new Date(b.date);
    return sortByDate === "ASC" ? dateA - dateB : sortByDate === "DESC" ? dateB - dateA : 0;
  });

  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = sortedAndFilteredRequestList.slice(indexOfFirstItem, indexOfLastItem);

  const totalPages = Math.ceil(sortedAndFilteredRequestList.length / itemsPerPage);

  const handlePageChange = (pageNumber) => {
    setCurrentPage(pageNumber);
  };

  const pageNumbers = [];
  for (let i = 1; i <= totalPages; i++) {
    pageNumbers.push(i);
  }

  const handleServiceFilter = (event) => {
    setSelectedService(event.target.value);
    setCurrentPage(1); 
  };

  const handleDateSort = (event) => {
    setSortByDate(event.target.value);
    setCurrentPage(1); 
  };

  return (
    <ProtectedRoute>
      <div className={styles.my_account_right} style={{ width: "100%" }}>
        <div className={page.invoice_title}>
          <h4>{pageData.acf.title_m}</h4>
          <form>
            <select
              className={page.service_sort}
              onChange={handleServiceFilter}
            >
              <option style={{ color: "black" }} value="">
              {locale === "ar" ? "ترتيب حسب: نوع الخدمة" : "Sort by: Service Type"}
              </option>
              {serviceList.map((list, index) => (
                <option
                  key={index}
                  style={{ color: "black" }}
                  value={list.slug}
                >
                  {list.title.rendered}
                </option>
              ))}
            </select>
            <select className={page.date_sort} onChange={handleDateSort}>
              <option value="">{locale === "ar" ? "ترتيب حسب: التاريخ" : "Sort by: Date"}</option>
              <option value="ASC">ASC</option>
              <option value="DESC">DESC</option>
            </select>
          </form>
        </div>
        <div
          className={
            langCode === "en"
              ? page.table_style_second
              : page.table_style_second_ar
          }
        >
          <table>
            <thead>
              <tr>
                {pageData.acf.table_head.map((hd, index) => (
                  <th key={index}>{hd.title}</th>
                ))}
              </tr>
            </thead>
            {currentItems.length > 0 ? (
              <tbody>
                {currentItems.map((doc, index) => {
                  const date = new Date(doc.date);
                  const formatted_date = date.toLocaleDateString("en-GB", {
                    day: "2-digit",
                    month: "short",
                    year: "numeric",
                  });

                  const serviceObj = serviceList.find(
                    (service) => service.slug === doc.acf.type_request
                  );
                  return (
                    <tr key={index}>
                      <td>{doc.acf.type_request}</td>
                      <td>
                        {doc.service_status_taxonomy
                          ? doc.service_status_taxonomy.term_name
                          : "Pending"}
                      </td>
                      <td>{formatted_date}</td>
                      <td>
                        {serviceObj?.acf.service_detail_card.report && (
                          <a
                            href={serviceObj.acf.service_detail_card.report.url}
                            className={`${
                              langCode === "en" ? page.pdf_link : page.pdf_link_ar
                            } text-primary`}
                            download={
                              serviceObj.acf.service_detail_card.report.title
                            }
                            target="_blank"
                          >
                            PDF
                            <Image
                              src="/images/dowload-primary.svg"
                              alt="icon"
                              width={15}
                              height={15}
                              priority
                            />
                          </a>
                        )}
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            ) : (
              <div
                style={{
                  padding: "30px 0",
                  textAlign: "center",
                  display: "block",
                  color: "white",
                }}
              >
                <p
                  style={{
                    position: "absolute",
                    width: "100%",
                    textAlign: "center",
                    bottom: "18px",
                  }}
                >
                {langCode === "en" ? "لا توجد طلبات" : "No Requests"} 
                </p>
              </div>
            )}
          </table>
        </div>
        {totalPages > 1 && (
          <div
            style={{
              width: "100%",
              display: "flex",
              gap: "10px 20px",
              justifyContent: "flex-end",
              paddingRight: "25px",
            }}
          >
            {pageNumbers.map((number) => (
              <button
                key={number}
                onClick={() => handlePageChange(number)}
                className={currentPage === number ? `${page.activePage}` : ""}
                style={{
                  background: "transparent",
                  color: currentPage === number ? "white" : "#787878",
                  border: "unset",
                }}
              >
                {number}
              </button>
            ))}
          </div>
        )}
      </div>
    </ProtectedRoute>
  );
};

export default Index;


export async function getServerSideProps({ locale }) {
  const langCode = locale === "ar" ? "ar" : "en";
  
  try {
    const pageData = await fetchPageBySlug("requested-services", langCode);
    const serviceList = await fetchServices(langCode);
    return {
      props: {
        pageData,
        serviceList
      },
    };
  } catch (error) {
    console.error("Failed to fetch data:", error);
    return {
      props: {
        pageData: [],
        serviceList: []
      },
    };
  }
}
