.invoice_title {
  display: flex;
  width: 100%;
  align-items: center;
  justify-content: space-between;
  row-gap: 20px;
}

.invoice_title h4 {
  color: #ffffff;
  font-size: 1.5625rem;
  line-height: 1;
  font-weight: 400;
  margin: 0;
}

.invoice_title form {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  column-gap: 15px;
  width: 430px;
}

.service_sort {
  width: 50%;
  text-align: center;
  background: transparent;
}

.date_sort {
  border: none;
  width: 45%;
  text-align: center;
  background: var(--color-primary);
}

.invoice_title select {
  height: 50px;
  padding: 0 20px;
  color: #ffffff;
  border-radius: 9px;
  border: 1px solid #ff5e3e;
  font-size: 12.91px;
  letter-spacing: 0.4px;
  background-image: url(/images/select_drop_arrow.svg);
  background-repeat: no-repeat;
  background-position: 91% 20px;
}
select.date_sort {
  background-image: url(/images/select_drop_arrow.svg);
  background-repeat: no-repeat;
  background-position: 84% 20px;
}

/* ------------------------------------------------------------------------ */

.table_style_second {
  width: 100%;
}

.table_style_second table {
  border-collapse: collapse;
  background: linear-gradient(
    180deg,
    rgba(255, 255, 255, 0.12) 0%,
    rgba(0, 84, 135, 0.12) 100%
  );
  backdrop-filter: blur(12px);
  border-radius: 19px;
}

.table_style_second table th {
  color: #ffffff;
  font-size: 1.125rem;
  padding: 23px 0;
  background: #005487;
  font-weight: 400;
  text-align: left;
  width: 20%;
  min-width: 100px;
}

.table_style_second table th:first-child {
  width: 35%;
}

.table_style_second table th:nth-child(2) {
  width: 25%;
}

.table_style_second td:first-child,
.table_style_second th:first-child {
  padding-left: 30px;
}

.table_style_second thead th:first-child {
  border-radius: 20px 0 0 0;
}

.table_style_second thead th:last-child {
  border-radius: 0 20px 0 0;
}

.table_style_second table td {
  color: #ffffff;
  font-size: 1rem;
  padding: 27px 0;
  font-weight: 400;
}

.table_style_second tbody tr {
  padding: 0 20px;
  position: relative;
}

.table_style_second tbody tr::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 20px; 
  right: 20px; 
  height: 1px;
  background: #6d758f;
}

.table_style_second tbody tr:last-child::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 20px; 
  right: 20px; 
  height: 1px;
  background: var(--color-secondary) !important;
}


/* ============ */

.table_style_second_ar {
  width: 100%;
}

.table_style_second_ar table {
  border-collapse: collapse;
  background: linear-gradient(
    180deg,
    rgba(255, 255, 255, 0.12) 0%,
    rgba(0, 84, 135, 0.12) 100%
  );
  backdrop-filter: blur(12px);
  border-radius: 19px;
}

.table_style_second_ar table th {
  color: #ffffff;
  font-size: 1.125rem;
  padding: 23px 0;
  background: #005487;
  font-weight: 400;
  text-align: start;
  width: 20%;
}

.table_style_second_ar table th:first-child {
  width: 35%;
}

.table_style_second_ar table th:nth-child(2) {
  width: 25%;
}

.table_style_second_ar td:first-child,
.table_style_second_ar th:first-child {
  padding-right: 30px;
}

.table_style_second_ar thead th:first-child {
  border-radius: 0 20px 0 0;
}

.table_style_second_ar thead th:last-child {
  border-radius: 20px 0 0 0;
}

.table_style_second_ar table td {
  color: #ffffff;
  font-size: 1rem;
  padding: 27px 0;
  font-weight: 400;
  text-align: start;
}

.table_style_second_ar tbody tr {
  padding: 0 20px;
  position: relative;
}

.table_style_second_ar tbody tr:not(:last-child)::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  margin: auto;
  width: calc(100% - 40px);
  height: 1px;
  background: #6d758f;
}

.table_style_second_ar tbody tr:last-child::after {
  content: "";
  background: var(--color-secondary) !important;
}

.pdf_link {
  display: flex;
  align-items: center;
  column-gap: 8px;
  text-transform: uppercase;
}

.pdf_link_ar {
  display: flex;
  align-items: center;
  column-gap: 8px;
  text-transform: uppercase;
  /* justify-content: flex-end; */
}

@media (max-width: 860px) {
  .table_style_second table td {
    padding: 15px 0;
  }

  .table_style_second table th {
    padding: 15px 0;
  }

  .table_style_second_ar table td {
    padding: 15px 0;
  }

  .table_style_second_ar table th {
    padding: 15px 0;
  }
}

@media (max-width: 767px) {
  .invoice_title {
    flex-wrap: wrap;
    justify-content: center;
  }

  .invoice_title select {
    height: 40px;
    padding: 0 15px;
  }

  .table_style_second table th {
    width: 20%;
  }

  .table_style_second table th:first-child {
    width: 35%;
  }

  .table_style_second table th:nth-child(2) {
    width: 25%;
  }

  .table_style_second thead th:first-child {
    border-radius: 15px 0 0 0;
  }

  .table_style_second thead th:last-child {
    border-radius: 0 15px 0 0;
  }

  .table_style_second td:first-child,
  .table_style_second th:first-child {
    padding-left: 15px;
  }
  /* ======= */

  .table_style_second_ar table th {
    width: 20%;
  }

  .table_style_second_ar table th:first-child {
    width: 35%;
  }

  .table_style_second_ar table th:nth-child(2) {
    width: 25%;
  }

  .table_style_second_ar thead th:first-child {
    border-radius: 0 15px 0 0;
  }

  .table_style_second_ar thead th:last-child {
    border-radius: 15px 0 0 0;
  }

  .table_style_second_ar td:first-child,
  .table_style_second_ar th:first-child {
    padding-left: 15px;
  }

  .invoice_title select {
    background-position: 95% 15px;
  }
}

@media (max-width: 600px) {
  .table_style_second table td {
    font-size: 0.8125rem;
  }

  .table_style_second_ar table td {
    font-size: 0.8125rem;
  }
}
