.login_page {
  padding-top: 180px;
}

.login_wrap {
  max-width: 616px;
  margin: auto;
  padding-bottom: 20px;
}

.login_title {
  margin-bottom: 35px;
}

.login_body ul {
  display: flex;
  flex-flow: column wrap;
  row-gap: 20px;
}

.login_body input {
  width: 100%;
  height: 50px;
  padding: 0 20px;
  background: #ffffff;
  border: none;
  letter-spacing: 0.5px;
  font-weight: 400;
  color: #111111;
  /* font-family: "precioussanstwo-medium"; */
  border-radius: 9px;
}

:global(body.rtl) .login_body input {
  font-family: var(--font-arabic);
}

.login_body input::placeholder {
  color: #111111;
  opacity: 1;
}

.login_body input::-ms-input-placeholder {
  color: #111111;
}

.btn_style_wrap {
  margin: 27px auto 0 auto;
}

.btn_style_primary {
  min-width: 159px;
}

.dont_have_account {
  display: block;
  text-align: center;
  color: #d7d7d7;
  margin-top: 40px;
}

.dont_have_account a {
  text-decoration: underline;
}

.dont_have_account a:hover {
  color: var(--color-primary);
}

.password_container {
  display: flex;
  background: #fff;
  border-radius: 9px;
  align-items: center;
  justify-content: space-between;
  padding-right: 15px;
}

.password_container>input {
  width: 90%;
}

.password_container>span {
  cursor: pointer;
}

.password_container_ar {
  display: flex;
  background: #fff;
  border-radius: 9px;
  align-items: center;
  justify-content: space-between;
  padding-left: 15px;
}

.password_container_ar>input {
  width: 90%;
}

.password_container_ar>span {
  cursor: pointer;
}

.select_wrapper {
  position: relative;
}

.select_wrapper>span {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  transition: transform 0.3s ease;
  cursor: pointer;
}

.select_wrapper_ar {
  position: relative;
}

.select_wrapper_ar>span {
  position: absolute;
  left: 15px;
  top: 50%;
  transform: translateY(-50%);
  transition: transform 0.3s ease;
  cursor: pointer;
}

/* .select_wrapper > span.rotate {
  transform: translateY(-50%) rotate(180deg);
} */

.login_body input::-webkit-inner-spin-button,
.login_body input::-webkit-calendar-picker-indicator {
  display: none;
  -webkit-appearance: none;
}

.login_body input::-webkit-contacts-auto-fill-button {
  visibility: hidden;
  display: none !important;
  pointer-events: none;
  position: absolute;
  right: 0;
}