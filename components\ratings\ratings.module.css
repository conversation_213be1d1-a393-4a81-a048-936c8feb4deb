.rating_card_item_en {
  /* background: #fff; */
  height: 550px;
  padding: 80px;
  background-position: right 20px top 20px;
  background-repeat: no-repeat;
  border-radius: var(--radius-30);
  overflow-y: auto;

  &::-webkit-scrollbar-track {
    margin-top: 30px;
    margin-bottom: 30px;


  }
}

.rating_card_item_en:nth-child(even) {
  background: rebeccapurple;
}

.rating_card_item_en:not(:first-child) {
  margin-top: 40px;
}

.rating_card_body {
  max-width: 430px;
}

.rating_card_body h3 {
  line-height: 52px;
  font-size: 3.2rem;
  font-weight: 600;
  color: var(--color-secondary);
  margin-bottom: 30px;
}

:global(body.rtl) .rating_card_body h3 {
  font-weight: 300;
  font-size: 3rem;
}

.rating_card_body p {
  margin: 0;
  color: #111111;
  text-align: justify;
}

.btn_style_wrap {
  margin-top: 30px;
}

.rating_card_item_ar {
  background-position: left 20px top 20px;
}

:global(body.rtl) .rating_card_body {
  /* max-width: 380px; */
}



@media (max-width: 1600px) {
  :global(body.rtl) .rating_card_body {
    /* max-width: 280px; */
  }

  .rating_card_item_en {
    max-height: 550px !important;
    height: 550px !important;
  }

  .rating_card_body h3 {
    font-size: 3rem;
  }
}

@media (max-width: 500px) {
  .rating_card_item_en {
    max-height: 550px !important;
    height: 550px !important;
  }
}

.rating_card_body {
  max-width: 47%;
}

@media (max-width: 1600px) {
  .rating_card_body {
    max-width: 52%;
  }

}



@media (max-width: 1440px) {
  .rating_card_body h3 {
    line-height: 40px;
    font-size: 2.4375rem;
    margin-bottom: 15px;
  }

  :global(body.rtl) .rating_card_body h3 {
    font-size: 2rem;
  }

}

@media (max-width: 1200px) {
  .btn_style_wrap {
    margin-top: 20px;
  }

  .rating_card_body h3 {
    line-height: 40px;
    font-size: 2.3rem;
    margin-bottom: 15px;
  }

  :global(body.rtl) .rating_card_body h3 {
    font-size: 2rem;
  }

  .rating_card_item_en {
    height: 550px;
    padding: 35px;
    background-size: 300px;
    background-position: right 40px bottom 30px;
  }

  .rating_card_item_ar {
    height: 550px;
    padding: 35px;
    background-size: 300px;
    background-position: left 40px bottom 30px;
  }

  .btn_style_wrap {
    margin-top: 10px;
  }

  :global(body.rtl) .rating_card_body {
    max-width: 480px;
  }
}

@media (max-width: 1040px) {
  .rating_card_body h3 {
    line-height: 34px;
    font-size: 2rem;
  }

  /* .rating_card_item_en {
        max-height: 320px;
    } */
}

@media (max-width: 1011px) {
  .rating_card_item_en {
    max-height: 600px;
  }

  :global(body.rtl) .rating_card_body {
    max-height: 600px;
  }
}

@media (max-width: 860px) {
  .rating_card_item_en {
    max-height: 500px;
  }

  .rating_card_body h3 {
    line-height: 28px;
    font-size: 1.5rem;
  }

  :global(body.rtl) .rating_card_body h3 {
    font-size: 1.3rem;
  }

  :global(body.rtl) .rating_card_body {
    max-width: 320px;
  }
}

/* @media (max-width: 820px) {
  .rating_card_item_en {
    max-height: 300px !important;
  }
} */

@media (max-width: 767px) {
  .rating_card_item_en {
    padding: 20px;
    max-height: 440px;
    background-image: initial !important;
  }

  :global(body.rtl) .rating_card_body {
    max-width: 100%;
  }

  .rating_card_body {
    max-width: 100%;
  }

  .rating_card_item_en {
    max-height: 460px !important;
    height: 460px !important;
  }

  .rating_card_item_ar {
    max-height: 460px !important;
    height: 460px !important;
  }

}

@media (max-width: 515px) {
  .rating_card_item_en {
    max-height: 480px;
  }
}

@media (max-width: 500px) {
  .rating_card_item_en {
    max-height: 480px !important;
    height: 480px !important;
  }

  .rating_card_item_ar {
    max-height: 480px !important;
    height: 480px !important;
  }
}

@media (max-width: 368px) {
  .rating_card_item_en {
    max-height: 420px;
  }

  .rating_card_item_ar {
    max-height: 420px;
  }
}