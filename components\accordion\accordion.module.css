.accordion {
    width: 100%;
    display: flex;
    flex-flow: column wrap;
    row-gap: 12px;
}

.accordionItem {
    background: #ffffff;
    padding: 16px 18px 16px 33px;
    width: 100%;
    border-radius: 18px;
}

.accordionHeader {
    cursor: pointer;
    position: relative;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.accordionTitle {
    color: #0B1F51;
    font-size: 1.25rem;
    font-weight: 400;
    font-family: 'precioussanstwo-bold';
    display: block;
    width: calc(100% - 50px);
}

.accordionHeader.active .icon img {
    transform: rotate(180deg);
}

.accordionHeader .icon {
    width: 37px;
    height: 37px;
    background: var(--color-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* .accordionContent:empty {
    display: none;
} */

.accordionContent {
    overflow: hidden;
    transition: all 0.6s ease-in-out; 
}

.accordionContent p {
    margin: 0; 
}



@media (max-width: 600px) {
    .accordionItem {
        border-radius: 10px;
        padding: 7px 12px 7px 12px;
    }

    .accordionTitle {
        font-size: 1rem;
    }
}