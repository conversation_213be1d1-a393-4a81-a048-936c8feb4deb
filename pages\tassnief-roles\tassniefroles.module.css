.sixcolumn {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--gap-24);
}

/* ---------------------------------------------- */

.section_third {
    padding: 120px 0 80px 0;
}

.section_third_wrap {
    display: flex;
    flex-wrap: wrap;
    row-gap: 40px;
    justify-content: space-between;
}

.section_third .title {
    width: calc(61.5% - 40px);
    padding-right: 50px;
}

.section_third .title h2 {
    color: #1F2E58;
    margin-bottom: 10px;
}

.section_third .content {
    width: 38.5%;
}

/* ---------------------------------------------- */

.section_fourth {
    row-gap: 120px;
}

.section_fourth,
.enhance_block_wrap {
    display: flex;
    flex-flow: column wrap;
}

@media (max-width: 1200px) {
    .section_third {
        padding: 80px 0;
    }

    .section_fourth {
        row-gap: 80px;
    }
}

@media (max-width: 860px) {
    .sixcolumn {
        grid-template-columns: repeat(3, 1fr);
    }

    .section_third {
        padding: 50px 0;
    }

    .section_fourth {
        row-gap: 50px;
    }

    .financialmarkets {
        row-gap: 30px;
    }
}

@media (max-width: 767px) {
    .sixcolumn {
        grid-template-columns: repeat(2, 1fr);
    }

    .section_third .title {
        width: 100%;
        padding:0;
    }

    .section_third .content {
        width: 100%;
    }

    .section_third .title h2 br {
        display: none;
    }
    .section_third .title br{
        display: none;
    }
}

@media (max-width: 600px) {
    .sixcolumn {
        grid-template-columns: repeat(1, 1fr);
    }
}