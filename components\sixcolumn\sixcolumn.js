import React from "react";
import Image from "next/image";
import Link from "next/link";
import styles from "./sixcolumn.module.css";
import { useRouter } from "next/router";


const Sixcolumn = ({ className, sixColumnItems, list, lang, text_size }) => {
  const { locale } = useRouter();
  const langCode = locale === "ar" ? "ar" : "en";
  return (
    <>
      {!list ? (
        <div
        data-aos="fade-up"
         className={`${styles.sixcolumn} ${className ? className : ""} `}>
          {sixColumnItems.map((item, index) => (
            <div
              key={index}
              className={`${styles.sixcolumn_item} ${
                langCode === "ar" ? "sixcolumn_item_ar" : ""
              } sixcolumn_item`}
              // data-aos="fade-up"
            >
              <div
                className={
                  langCode === "ar"
                    ? styles.sixcolumn_bg_ar
                    : styles.sixcolumn_bg
                }
                style={{ backgroundImage: `url$(${item.icon})` }}
              ></div>
              <div
                className={`${styles.sixcolumn_inside} sixcolumn_inside text_size`}
              >
                <h4 dangerouslySetInnerHTML={{ __html: item.title }} />
                <p dangerouslySetInnerHTML={{ __html: item.description }} />
                {item.button && (
                  <Link
                    href={item.button.url || "#"}
                    className={`${styles.btn_style_wrap} ${
                      langCode === "ar" ? "btn_style_wrap_ar" : ""
                    } btn_style_wrap`}
                  >
                    <span
                      className={`${
                        langCode === "ar" ? "btn_style_primary_ar" : ""
                      } btn_style_primary`}
                    >
                      {item.button.title || "Learn More"}
                    </span>
                    <span
                      className={`${styles.btn_style_arrow} ${
                        langCode === "ar" ? "btn_style_arrow_ar" : ""
                      } ${"btn_style_arrow"}`}
                    >
                      <Image
                        src={
                          langCode === "ar"
                            ? "/images/white_arrow_left.png"
                            : "/images/white_right_arw.svg"
                        }
                        alt="icon"
                        width={22}
                        height={15}
                        priority
                      />
                    </span>
                  </Link>
                )}
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div
        data-aos="fade-up" className={`${styles.sixcolumn} ${className ? className : ""}`}>
          {sixColumnItems.map((item) => (
            <div
              key={item.id}
              className={`${styles.sixcolumn_item} ${
                langCode === "ar" ? "sixcolumn_item_ar" : ""
              } sixcolumn_item`}
              // data-aos="fade-up"
            >
              <div
                className={
                  langCode === "ar"
                    ? styles.sixcolumn_bg_ar
                    : styles.sixcolumn_bg
                }
                style={{
                  backgroundImage: `url(${item.acf.service_detail_card.icon})`,
                }}
              ></div>
              <div
                className={styles.sixcolumn_inside}
                style={{ marginTop: "20px" }}
              >
                <h4
                  dangerouslySetInnerHTML={{
                    __html: item.acf.service_detail_card.title,
                  }}
                />
                {/* <p
                  dangerouslySetInnerHTML={{
                    __html: item.acf.service_detail_card.description,
                  }}
                  style={{textAlign:'justify'}}
                /> */}
                <div
                  className={styles.ContentStyle}
                  style={{ textAlign: "justify" }}
                  dangerouslySetInnerHTML={{
                    __html: item.acf.service_detail_card.description,
                  }}
                />
                {item.acf.service_detail_card.button && (
                  <Link
                    href={
                      item.acf.service_detail_card.button.url
                        ? `${item.acf.service_detail_card.button.url}?service=${encodeURIComponent(item.acf.service_detail_card.button.title)}`
                        : "#"
                    }
                    className={`${styles.btn_style_wrap} ${
                      langCode === "ar" ? "btn_style_wrap_ar" : ""
                    } btn_style_wrap`}
                  >
                    <span
                      className={`${
                        langCode === "ar" ? "btn_style_primary_ar" : ""
                      } btn_style_primary`}
                    >
                      {item.acf.service_detail_card.button.title ||
                        "Learn More"}
                    </span>
                    <span
                      className={`${
                        langCode === "ar" ? "btn_style_arrow_ar" : ""
                      } ${"btn_style_arrow"}`}
                    >
                      <Image
                        src={
                          langCode == "ar"
                            ? "/images/white_arrow_left.png"
                            : "/images/white_right_arw.svg"
                        }
                        alt="icon"
                        width={22}
                        height={15}
                        priority
                      />
                    </span>
                  </Link>
                )}
              </div>
            </div>
          ))}
        </div>
      )}
    </>
  );
};

export default Sixcolumn;
