import { useState, useEffect } from "react";
import Image from "next/image";
import styles from "./careers.module.css";
import SubBanner from "@/components/innerbanner/innerbanner";
import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/css";
import CareerList from "@/components/carrer-list/career_list";
import Pagemenu from "@/components/pagemenu/pagemenu";
import { fetchCareer, fetchPageBySlug } from "@/lib/api/PageBySlug";
import Head from "next/head";
// const careerlist = [
// 	{
// 		id: 1,
// 		title: "Sales Executive",
// 		date: "Posted : 4 Days ago",
// 		details:
// 			"Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium, totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt explicabo.",
// 		link: "/career-details",
// 	},

// 	{
// 		id: 2,
// 		title: "Sales Executive",
// 		date: "Posted : 4 Days ago",
// 		details:
// 			"Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium, totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt explicabo.",
// 		link: "/career-details",
// 	},

// 	{
// 		id: 3,
// 		title: "Sales Executive",
// 		date: "Posted : 4 Days ago",
// 		details:
// 			"Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium, totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt explicabo.",
// 		link: "/career-details",
// 	},

// 	{
// 		id: 4,
// 		title: "Sales Executive",
// 		date: "Posted : 4 Days ago",
// 		details:
// 			"Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium, totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt explicabo.",
// 		link: "/career-details",
// 	},

// 	{
// 		id: 5,
// 		title: "Sales Executive",
// 		date: "Posted : 4 Days ago",
// 		details:
// 			"Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium, totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt explicabo.",
// 		link: "/career-details",
// 	},

// 	{
// 		id: 6,
// 		title: "Sales Executive",
// 		date: "Posted : 4 Days ago",
// 		details:
// 			"Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium, totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt explicabo.",
// 		link: "/career-details",
// 	},

// 	{
// 		id: 7,
// 		title: "Sales Executive",
// 		date: "Posted : 4 Days ago",
// 		details:
// 			"Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium, totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt explicabo.",
// 		link: "/career-details",
// 	},
// ];

// const menuItems = [
// 	{ name: "Roles we recruit for", link: "/" },
// 	{ name: "Apply for jobs", link: "/" },
// ];

function Careers({pageData, careerList}) {
	const banner = pageData.acf.banner_details || '';
	return (
    <div>
      <SubBanner bannerDetails={banner} alignEnd={false} />
      <Pagemenu isTab={true} menuItems={pageData.acf.tabs} />
      <div class="bg_main center_bg_2">
        <section
          className={`${styles.careers_top_section} pt-100 bg-secondary`}
        >
          <div className="container relative">
            <div class="overview_title" data-aos="fade-up">
              <h2
                className="main_title text-white"
                dangerouslySetInnerHTML={{ __html: pageData.acf.details.title }}
              />
              <p
                dangerouslySetInnerHTML={{
                  __html: pageData.acf.details.description,
                }}
              />
            </div>
          </div>
        </section>

        <section
          className={`${styles.careers_list_section} pt-50 bg-secondary`}
        >
          <div className="container  relative">
            <h2 className="main_title text-white" data-aos="fade-up">
              Latest Opening
            </h2>
            <CareerList careerlist={careerList} />
          </div>
        </section>
      </div>
    </div>
  );
}

export default Careers;

export async function getStaticProps({ locale }) {
	const langCode = locale === "ar" ? "ar" : "en";
	try {
		const pageData = await fetchPageBySlug("career", langCode);
		const careerList = await fetchCareer(langCode);

		return {
			props: {
				pageData,
				careerList,
			},
			revalidate: 10, 
		};
	} catch (error) {
		console.error("Failed to fetch data:", error);
		return {
			props: {
				pageData: null,
				careerList: null,
			},
		};
	}
}
