
import React, { useEffect, useState } from "react";
import Link from "next/link";
import { useRouter } from "next/router"; 
import styles from "./sidemenu.module.css";
import {fetchSideMenu} from "@/lib/api/byfield"


const Sidemenu = () => {
	const [menus, setMenus] = useState(null);
  const [loading, setLoading] = useState(true);
  const [message, setMessage] = useState('');
  
  const router = useRouter(); 
	const handleLogout = () => {
        localStorage.removeItem('token');
        localStorage.removeItem('user');
		localStorage.removeItem('membershipType');
        setMessage('You have logged out successfully.');
        router.push('/login'); 
    };

	useEffect(() => {
		const fetchData = async () => {
		try {
			const data = await fetchSideMenu(router.locale);
			setMenus(data);
		} catch (error) {
			console.error("Failed to fetch data:", error);
		} finally {
			setLoading(false);
		}
		};

		fetchData();
	}, []);

	if (loading) {
		return  <div  style={{minHeight:'250px'}}></div>; 
	}

	if (!menus) {
		return <div>.</div>; 
	}
	
	return (
		<ul className={styles.sidemenu}>
			{menus.menu_list.map((item, index) => (
				<li key={index}>
					<Link
						href={item.menu_item.url}
						className={`${styles.sidemenu_link} ${
							router.pathname === item.menu_item.url ? styles.active : ""
						}`}
					>
						{item.menu_item.title}
					</Link>
				</li>
			))}
				<li >
					<a
						style={{cursor:'pointer'}}
						className={`${styles.sidemenu_link} `}
						onClick={handleLogout}
					>
						{router.locale === "ar" ? 'تسجيل الخروج' : 'Logout'}
					</a>
				</li>
		</ul>
	);
};

export default Sidemenu;
