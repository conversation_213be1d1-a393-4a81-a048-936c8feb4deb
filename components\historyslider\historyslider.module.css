/* ------------------------------------------------------------ */

.historyslider_item {
    display: flex;
    flex-wrap: wrap;
    row-gap: 30px;
    justify-content: space-between;
    align-items: center;
}

.historyslider_content {
    color: #ffffff;
    width: 47.5%;
}

.historyslider_content h3 {
    color: #ffffff;
    margin-bottom: 16px;
}

.historyslider_content p:not(:first-child) {
    margin-top: 25px;
}

.historyslider_image {
    width: 40%;
    border-radius: 10px;
    overflow: hidden;
}

.historyslider_thumb {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    column-gap: 45px;
    row-gap: 15px;
    margin-bottom: 60px;
    justify-content: center;
}

.historyslider_thumb li {
    text-align: center;
    color: var(--color-primary);
    padding-bottom: 18px;
    display: block;
    font-size: 20px;
    font-weight: 400;
    font-family: var(--font-secondary);
    border-bottom: 4px solid rgba(255, 255, 255, .10);
    cursor: pointer;
    position: relative;
}

.historyslider_thumb li::after {
    content: "";
    position: absolute;
    left: 0;
    bottom: -4px;
    height: 4px;
    width: 100%;
    background: rgba(255, 255, 255, .10);
    transform: scaleX(0);
    /* Initial state: not visible */
    transform-origin: left center;
    transition: transform 0.5s ease-in-out;
    /* Smooth transition for interaction */
}

:global(body.rtl) .historyslider_thumb li::after {
    left: unset !important;
    right: 0 !important;
    transform-origin: right center !important;

}

.historyslider_thumb .active::after {
    background: var(--color-primary);
    animation: progressBar 2s forwards;
}

@keyframes progressBar {
    0% {
        transform: scaleX(0);
        /* Start with zero width */
    }

    100% {
        transform: scaleX(1);
        /* End with full width */
    }
}




@media (max-width: 1200px) {
    .historyslider_thumb {
        margin-top: 50px;
    }
}

@media (max-width: 860px) {

    .historyslider_image,
    .historyslider_content {
        width: 100%;
    }

    .historyslider_thumb {
        column-gap: 25px;
        grid-template-columns: repeat(4, 1fr);
    }

    .historyslider_thumb li {
        padding-bottom: 12px;
        font-size: 16px;
        border-bottom: 2px solid rgba(255, 255, 255, .10);
    }

    .historyslider_thumb li::after {
        bottom: -2px;
        height: 2px;
    }
}

@media (max-width: 600px) {
    .historyslider_thumb {
        margin-top: 30px;
    }

    .historyslider_thumb li {
        padding-bottom: 8px;
        font-size: 12px;
    }

    .historyslider_thumb {
        column-gap: 15px;
    }
}