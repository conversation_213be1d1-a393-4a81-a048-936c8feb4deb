.newsslider_image,
.newsslider_image img {
  border-radius: 15px;
}

.newsslider_image img {
  width: 100%;
}

.newsslider_image {
  position: relative;
}

.newsslider_content {
  padding-top: 20px;
}
.newsslider_content a {
  display: -webkit-box;
  -webkit-line-clamp: 2; /* Limit text to 2 lines */
  -webkit-box-orient: vertical;
  overflow: hidden;
  }
.newsslider_date {
  padding-left: 40px;
  position: relative;
  color: var(--color-primary);
  display: block;
  font-size: 0.875rem;
  margin-bottom: 12px;
}
.newsslider_date_ar{
  padding-right: 40px;
  position: relative;
  color: var(--color-primary);
  display: block;
  font-size: 0.875rem;
  margin-bottom: 12px;
}

.newsslider_date::after {
  content: "";
  width: 25px;
  height: 1px;
  background: var(--color-primary);
  position: absolute;
  left: 0;
  top: 50%;
  content: "";
  transform: translateY(-50%);
}
.newsslider_date_ar::after{
  content: "";
  width: 25px;
  height: 1px;
  background: var(--color-primary);
  position: absolute;
  right: 0;
  top: 50%;
  content: "";
  transform: translateY(-50%);
}
.newsslider_discription {
  font-size: 1.125rem;
  color: #fff;
  font-weight: 500 !important;
  -webkit-font-smoothing: antialiased; 
  -moz-osx-font-smoothing: grayscale;
  max-width: 434px;
}

.newsslider_discription:hover{
 color: var(--color-primary);
}

.news_tag {
  position: absolute;
  top: 12px;
  left: 12px;
  min-width: 72px;
  color: var(--color-secondary);
  text-transform: capitalize;
  font-weight: 600;
  font-size: 0.875rem;
  text-align: center;
  display: inline-block;
  padding: 4px 11px;
  background: rgba(255, 255, 255, 0.75);
  border-radius: 9px;
  z-index: 1;
}

/* @supports (-webkit-appearance: none) {
  .newsslider_discription {
    font-weight: 500; 
  }
} */

@media (max-width: 860px) {
  .newsslider_content {
    padding-top: 15px;
  }
}
