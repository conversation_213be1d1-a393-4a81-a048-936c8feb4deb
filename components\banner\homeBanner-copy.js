import React, { useRef, useState, useEffect } from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/css";
import "swiper/css/free-mode";
import "swiper/css/pagination";
import { FreeMode, Autoplay } from "swiper/modules";

import Link from "next/link";
import Image from "next/image";
import styles from "./homeBanner.module.css";
import { fetchRatings } from "@/lib/api/PageBySlug";
import { useRouter } from "next/router";

function HomeBanner({ bannerData }) {
  const { locale } = useRouter();
  const langCode = locale === "ar" ? "ar" : "en";
  const [lc, setLc] = useState(locale);

  const [options, setOptions] = useState([]);
  useEffect(() => {
    const fetchOptions = async () => {
      try {
        const newsList = await fetchRatings(langCode, "5");
        setOptions(newsList);
      } catch (error) {
        console.error("Failed to fetch data:", error);
      }
    };

    fetchOptions();
  }, [lc]);

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      // weekday: "long",
      year: "numeric",
      month: "short",
      day: "2-digit",
    });
  };
  const trimWords = (text, wordLimit) => {
    const words = text.split(" ");
    if (words.length > wordLimit) {
      return words.slice(0, wordLimit).join(" ") + "...";
    }
    return text;
  };
  const swiperRef = useRef(null);
  useEffect(() => {
    if (swiperRef.current && swiperRef.current.swiper) {
      const swiperInstance = swiperRef.current.swiper;
      swiperInstance.autoplay.start();
    }
  }, [options]);

  return (
    <section className={styles.home_banner}>
      <video
        width="320"
        height="240"
        className={styles.banner_video}
        autoPlay
        loop
        muted
        playsInline
        preload="metadata"
      >
        <source src={bannerData.video} type="video/mp4" />
        <source src={bannerData.video} type="video/ogg" />
        Your browser does not support the video tag.
      </video>
      <div className="container">
        <div className={styles.banner_content}>
          <h1
            data-aos="fade-up"
            dangerouslySetInnerHTML={{ __html: bannerData.title }}
          />
          {bannerData.button && bannerData.button.url && (
            <Link
              data-aos="fade-up"
              data-aos-delay="500"
              href={bannerData.button.url}
              className={`${styles.btn_style_wrap} btn_style_wrap`}
            >
              <span className="btn_style_primary">
                {bannerData.button.title}
              </span>
              <span
                className={`${
                  langCode === "ar" ? "btn_style_arrow_ar" : ""
                } ${"btn_style_arrow"}`}
              >
                <Image
                  src={
                    langCode === "ar"
                      ? "/images/white_arrow_left.png"
                      : "/images/white_right_arw.svg"
                  }
                  alt="icon"
                  width={22}
                  height={15}
                  priority
                />
              </span>
            </Link>
          )}
        </div>
      </div>
      <div className={`${styles.banner_marquee} banner_marquee`}>
        <div className={`${styles.container} container`}>
          <Swiper
            ref={swiperRef}
            slidesPerView={"auto"}
            key={langCode}
            spaceBetween={34}
            freeMode={true}
            speed={8000}
            autoplay={{
              delay: 1,
              disableOnInteraction: false,
            }}
            loop={true}
            modules={[FreeMode, Autoplay]}
            className="marquee_slider"
          >
            {options.map((item, index) => (
              <SwiperSlide key={index}>
                <p className={styles.marquee_item}>
                  <span style={{  margin:'0px '}}>
                    <Image
                      src="/images/date-primary.svg"
                      alt="date"
                      width={17}
                      height={17}
                      priority
                    />
                    {formatDate(item.acf.issue_date)}
                  </span>
                  {/* {trimWords(item.title.rendered, 15)} */}
                  <span style={{ color: 'white' , margin:'0px '}}>
                  Tassnief rated&nbsp;<span dangerouslySetInnerHTML={{ __html: item.title.rendered }} style={{margin:'0px'}}/>&nbsp;with&nbsp;{item.acf.rating_.label}&nbsp;Rating
                  </span>

                </p>
              </SwiperSlide>
            ))}
          </Swiper>
        </div>
      </div>
    </section>
  );
}

export default HomeBanner;
