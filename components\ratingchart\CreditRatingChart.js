// components/CreditRatingChart.js
import styles from "./CreditRatingChart.module.css";
import Image from "next/image";

const CreditRatingChart = () => {
	return (
		<div className={`${styles.container} ${styles.rating_img_wrapper}`}>
			<Image loading='lazy' src="/images/RatingChart.png" alt="RatingChart" width={1136} height={850} />

			<div className={styles.longTerm} style={{ display: "none" }}>
				<h2>Long-Term</h2>
				<div className={styles.longTerm_item} style={{ "--color": "#E8593B" }}>
					<div className={styles.rating_left}>
						<span>Classification</span>
						<span>
							<b>High Grade</b>
						</span>
					</div>
					<div className={styles.rating_center}>
						<ul>
							<li>
								AAA
								<span className={styles.shortRating}>T-1</span>
							</li>
							<li>AA+</li>
							<li>AAA</li>
							<li>AA-</li>
						</ul>
					</div>
					<div className={styles.rating_right}>
						<span>Risk Level</span>
						<span>
							<b>Negligible</b>
						</span>
					</div>
				</div>
				<div className={styles.longTerm_item} style={{ "--color": "#E8593B" }}>
					<div className={styles.rating_left}>
						<span>Classification</span>
						<span>
							<b>Investment Grade</b>
						</span>
					</div>
					<div className={styles.rating_center}>
						<ul>
							<li>A+</li>
							<li>A</li>
							<li>A-</li>
							<li>BBB+</li>
							<li>BBB</li>
							<li>BBB-</li>
						</ul>
					</div>
					<div className={styles.rating_right_multi}>
						<div className={styles.rating_right}>
							<span>Risk Level</span>
							<span>
								<b>Very Low</b>
							</span>
						</div>
						<div className={styles.rating_right}>
							<span>Risk Level</span>
							<span>
								<b>Very Low</b>
							</span>
						</div>
					</div>
				</div>

				<div className={styles.longTerm_item} style={{ "--color": "#F8AD28" }}>
					<div className={styles.rating_left}>
						<span>Classification</span>
						<span>
							<b>Near Prime</b>
						</span>
					</div>
					<div className={styles.rating_center}>
						<ul>
							<li>BB+</li>
							<li>BB</li>
							<li>BB-</li>
						</ul>
					</div>
					<div className={styles.rating_right}>
						<span>Risk Level</span>
						<span>
							<b>Low to Moderate</b>
						</span>
					</div>
				</div>

				<div className={styles.longTerm_item} style={{ "--color": "#F8AD28" }}>
					<div className={styles.rating_left}>
						<span>Classification</span>
						<span>
							<b>Sum Prime</b>
						</span>
					</div>
					<div className={styles.rating_center}>
						<ul>
							<li>B+</li>
							<li>B</li>
							<li>B-</li>
						</ul>
					</div>
					<div className={styles.rating_right}>
						<span>Risk Level</span>
						<span>
							<b>High</b>
						</span>
					</div>
				</div>
				<div className={styles.longTerm_item} style={{ "--color": "#EA7627" }}>
					<div className={styles.rating_left}>
						<span>Classification</span>
						<span>
							<b>Credit Watch</b>
						</span>
					</div>
					<div className={styles.rating_center}>
						<ul>
							<li>ccc+</li>
							<li>ccc</li>
							<li>ccc-</li>
						</ul>
					</div>
					<div className={styles.rating_right}>
						<span>Risk Level</span>
						<span>
							<b>Very High</b>
						</span>
					</div>
				</div>
				<div className={styles.longTerm_item} style={{ "--color": "#D32400" }}>
					<div className={styles.rating_left}>
						<span>Classification</span>
						<span>
							<b>Distressed</b>
						</span>
					</div>
					<div className={styles.rating_center}>
						<ul>
							<li>cc</li>
							<li>c</li>
							<li>d</li>
						</ul>
					</div>
					<div className={styles.rating_right}>
						<span>Risk Level</span>
						<span>
							<b>Extremely high</b>
						</span>
						<span>
							<b>Default</b>
						</span>
					</div>
				</div>
			</div>
		</div>
	);
};

export default CreditRatingChart;
