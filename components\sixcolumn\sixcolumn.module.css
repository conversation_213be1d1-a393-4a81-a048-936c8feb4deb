.sixcolumn {
  row-gap: 25px;
}

.sixcolumn_item {
  padding: 40px;
  background-color: #ffffff;
  display: flex;
  flex-wrap: wrap;
  min-height: auto;
  position: relative;
  border-radius: var(--radius-34);
  overflow: hidden;
  z-index: 0;
  transition: all 0.3s ease-in-out;

}

.sixcolumn_bg {
  position: absolute;
  top: 45px;
  right: 0;
  width: 100%;
  height: 100%;
  background-position: top 15px right 20px;
  background-repeat: no-repeat;
  z-index: -1;
}

.sixcolumn_item::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 101%;
  height: 101%;
  background: linear-gradient(
    113.49deg,
    rgba(232, 89, 59, 1) 0%,
    rgba(0, 40, 85, 1) 100%
  );
  opacity: 0;
  visibility: hidden;
  /* transition: opacity 0.2s ease-in-out, visibility 0.2s ease-in-out; */
  transition: all 0.3s ease-in-out;
  border-radius: var(--radius-34);
  z-index: -2;
}

.sixcolumn_item h4 {
  color: #0b1f51;
  font-size: 1.5rem;
  margin-bottom: 15px;
}

.sixcolumn_item p:last-child {
  margin-bottom: 20px !important;
}

.sixcolumn_inside {
  max-width: 430px;
  /* margin-top: 103px; */
}

.sixcolumn_inside p{
  text-align: justify;
}

.btn_style_wrap {
  margin-top: 32px;
}

/* -------------------------------------------------------------------- */

.sixcolumn_item:hover:after {
  opacity: 1;
  visibility: visible;
}

.sixcolumn_item:hover {
  background: transparent;
  background-repeat: no-repeat;
}

.sixcolumn_item:hover h4,
.sixcolumn_item:hover p, .sixcolumn_item:hover li {
  color: #ffffff;
}
.sixcolumn_item li {
  line-height: 24px;
}


.sixcolumn_bg_ar {
  position: absolute;
  top: 45px;
  left: 0;
  width: 100%;
  height: 100%;
  background-position: top 15px left 20px;
  background-repeat: no-repeat;
  z-index: -1;
}

.ContentStyle ul li {
  padding-bottom: 5px;
  text-align: justify;
}
.ContentStyle ul {
  list-style: disc;
  padding-left: 18px;
}

@media (max-width: 1200px) {
  .btn_style_wrap {
    margin-top: 15px;
  }

  .sixcolumn_item {
    padding: 25px;
    min-height: 300px;
  }

  .sixcolumn_bg {
    background-size: 130px;
  }
  /* .sixcolumn_inside {
    margin-top: 90px;
  } */

  .sixcolumn_bg_ar {
    background-size: 130px;
  }
}

@media (max-width: 860px) {
  .sixcolumn {
    row-gap: 15px;
  }

  .sixcolumn_item {
    padding: 20px;
    min-height: 220px;
  }

  .sixcolumn_item h4 {
    line-height: 1.1;
    margin-bottom: 10px;
  }
  /* .sixcolumn_inside {
    margin-top: 70px;
  } */
}


