import React from "react";
import styles from "./tassniefroles.module.css";
import SubBanner from "@/components/innerbanner/innerbanner";
import Pagemenu from "@/components/pagemenu/pagemenu";
import Sixcolumn from "@/components/sixcolumn/sixcolumn";
import Financialmarkets from "@/components/financialmarkets/financialmarkets";
import Threecolumn from "@/components/threecolumn/threecolumn";
import { fetchPageBySlug } from "@/lib/api/PageBySlug";
import {fetchaboutTab} from "@/lib/api/byfield";




const Index = ({pageData,Tabs}) => {
	const banner = pageData.acf.banner_details || '';
	const details= pageData.acf.details_role || '';
	const help= pageData.acf.help_details || '';
	const transparency= pageData.acf.enhance_transparency || '';
	const std= pageData.acf.the_standardization || '';
	return (
		<>
			<SubBanner
				bannerDetails={banner}
				alignEnd={false}
			/>
			<Pagemenu menuItems={Tabs.tabs} />
			<section className="py-120 bg-secondary">
				<div className="container">
					<div className="text-white mb-50" data-aos="fade-up">
						<h2 className="main_title mb-20"
							dangerouslySetInnerHTML={{ __html: details.title }}
						/>
						<p className="color-pera"
							dangerouslySetInnerHTML={{ __html: details.description }}
						/>
					</div>
					<Sixcolumn
						className={styles.sixcolumn}
						sixColumnItems={details.boxs}
					/>
				</div>
			</section>
			<section className={styles.section_third}>
				<div className={`${styles.section_third_wrap} container`}>
					<div className={styles.title} data-aos="fade-right">
						<h2 className="main_title"
							dangerouslySetInnerHTML={{ __html: help.title }}
						/>

						<p dangerouslySetInnerHTML={{ __html: help.description }} />
					</div>
					<div className={styles.content} data-aos="fade-left">
						<Financialmarkets list={help.boxs} />
					</div>
				</div>
			</section>

			<section
				className={`${styles.section_fourth} pt-120 bg-secondary section_fourth`}
			>
				<div className={styles.enhance_block}>
					<div className={`${styles.enhance_block_wrap} container`}>
						<div
							className={`${styles.title} text-white text-center mb-50`}
							data-aos="fade-up"
						>
							<h2 className="main_title mb-20" dangerouslySetInnerHTML={{ __html: transparency.title }} />
							<p className="color-pera" 
								dangerouslySetInnerHTML={{ __html: transparency.description }}
							/>
						</div>
						<Threecolumn columns={transparency.boxs} />
					</div>
				</div>
				<div className={styles.standardization}>
					<div className={`${styles.standardization_wrap} container`}>
						<div className="text-white mb-50 text-center" data-aos="fade-up">
							<h2 className="main_title mb_20" 
								dangerouslySetInnerHTML={{ __html: std.title }}
							/>
							<p className="color-pera"
								dangerouslySetInnerHTML={{ __html: std.description }}
							/>
						</div>
						<Sixcolumn
							sixColumnItems={std.boxs}
							className={styles.sixcolumn}
						/>
					</div>
				</div>
			</section>
		</>
	);
};

export default Index;

export async function getServerSideProps({locale}) {
	const langCode = locale === "ar" ? "ar" : "en";
	try {
		const pageData = await fetchPageBySlug("tassnief-roles", langCode);
		const Tabs = await fetchaboutTab(langCode);
		return {
			props: {
				pageData,
				Tabs
			},
		};
	} catch (error) {
		console.error("Failed to fetch data:", error);
		return {
			props: {
				pageData: null
			},
		};
	}
}